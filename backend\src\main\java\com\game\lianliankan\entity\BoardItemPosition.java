package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "board_item_position",
       indexes = @Index(name = "idx_board_position", columnList = "board_id, x, y"))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoardItemPosition {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "board_id", nullable = false)
    private Long boardId;

    @Column(name = "item_id", nullable = false)
    private Long itemId;

    @Column(name = "x", nullable = false)
    private Integer xPosition;

    @Column(name = "y", nullable = false)
    private Integer yPosition;

    @Column(name = "is_removed")
    private Boolean isRemoved = false;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "board_id", insertable = false, updatable = false)
    private GameBoard gameBoard;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    private GameItem gameItem;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
