package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.GameSessionDTO;
import com.game.lianliankan.service.GameService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/session")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class GameSessionController {
    
    private final GameService gameService;
    
    /**
     * 创建新的游戏会话
     */
    @PostMapping("/create")
    public ApiResponse<GameSessionDTO.CreateResponse> createGameSession(@RequestBody GameSessionDTO.CreateRequest request) {
        try {
            GameSessionDTO.CreateResponse response = gameService.createNewGameSession(request);
            return ApiResponse.success("创建游戏会话成功", response);
        } catch (Exception e) {
            log.error("创建游戏会话失败", e);
            return ApiResponse.error("创建游戏会话失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取游戏会话状态
     */
    @GetMapping("/{sessionId}")
    public ApiResponse<GameSessionDTO.GameStateResponse> getGameSessionState(@PathVariable String sessionId) {
        try {
            GameSessionDTO.GameStateResponse response = gameService.getGameSessionState(sessionId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取游戏会话状态失败", e);
            return ApiResponse.error("获取游戏会话状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新游戏会话状态
     */
    @PostMapping("/update")
    public ApiResponse<Void> updateGameSessionState(@RequestBody GameSessionDTO.UpdateRequest request) {
        try {
            gameService.updateGameSessionState(request);
            return ApiResponse.success("更新游戏状态成功", null);
        } catch (Exception e) {
            log.error("更新游戏会话状态失败", e);
            return ApiResponse.error("更新游戏会话状态失败: " + e.getMessage());
        }
    }
}
