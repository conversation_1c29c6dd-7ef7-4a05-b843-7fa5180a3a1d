package com.game.lianliankan.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

public class ItemSelectionDTO {
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemInfo {
        private Long id;
        private String name;
        private String imagePath;
        private Integer positionX;
        private Integer positionY;
        private String status; // default, pending, confirmed
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameStateResponse {
        private String sessionId;
        private String playerName;
        private List<ItemInfo> items;
        private Integer pendingCount;
        private Integer confirmedCount;
        private Boolean isCompleted;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateSessionRequest {
        private String playerName;
        private String difficultyLevel = "normal"; // normal, hard
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateSessionResponse {
        private String sessionId;
        private String playerName;
        private List<ItemInfo> items;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateStateRequest {
        private String sessionId;
        private List<ItemInfo> items;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfirmSelectionRequest {
        private String sessionId;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResetGameRequest {
        private String sessionId;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportRequest {
        private String sessionId;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportResponse {
        private String exportCode;
        private String playerName;
        private Integer pendingCount;
        private Integer confirmedCount;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportRequest {
        private String sessionId;
        private String importCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifficultyLevel {
        private String level;
        private String name;
        private String description;
        private Boolean isDefault;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifficultyLevelsResponse {
        private List<DifficultyLevel> levels;
    }
}
