package com.game.lianliankan.repository;

import com.game.lianliankan.entity.BoardItemPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BoardItemPositionRepository extends JpaRepository<BoardItemPosition, Long> {
    
    List<BoardItemPosition> findByBoardId(Long boardId);
    
    List<BoardItemPosition> findByBoardIdAndIsRemovedFalse(Long boardId);
    
    @Query("SELECT b FROM BoardItemPosition b WHERE b.boardId = :boardId " +
           "AND b.xPosition = :x AND b.yPosition = :y AND b.isRemoved = false")
    List<BoardItemPosition> findByBoardIdAndPosition(@Param("boardId") Long boardId, 
                                                     @Param("x") Integer x, 
                                                     @Param("y") Integer y);
}
