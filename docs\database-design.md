# 数据库设计

## 表结构设计

### 1. 游戏物品表 (game_items)
存储游戏中的物品信息

```sql
CREATE TABLE game_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '物品名称',
    image_path VARCHAR(200) NOT NULL COMMENT '图片路径',
    width INT NOT NULL DEFAULT 1 COMMENT '物品宽度(格子数)',
    height INT NOT NULL DEFAULT 1 COMMENT '物品高度(格子数)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 游戏棋盘表 (game_board)
存储棋盘的默认配置

```sql
CREATE TABLE game_board (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '棋盘名称',
    width INT NOT NULL DEFAULT 10 COMMENT '棋盘宽度',
    height INT NOT NULL DEFAULT 9 COMMENT '棋盘高度',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认棋盘',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 棋盘物品位置表 (board_item_positions)
存储物品在棋盘上的位置

```sql
CREATE TABLE board_item_positions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    board_id BIGINT NOT NULL COMMENT '棋盘ID',
    item_id BIGINT NOT NULL COMMENT '物品ID',
    x_position INT NOT NULL COMMENT 'X坐标',
    y_position INT NOT NULL COMMENT 'Y坐标',
    is_removed BOOLEAN DEFAULT FALSE COMMENT '是否已被消除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (board_id) REFERENCES game_board(id),
    FOREIGN KEY (item_id) REFERENCES game_items(id),
    INDEX idx_board_position (board_id, x_position, y_position)
);
```

### 4. 游戏进度表 (game_progress)
存储玩家的游戏进度

```sql
CREATE TABLE game_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    progress_code VARCHAR(500) NOT NULL COMMENT '进度代码',
    board_state TEXT NOT NULL COMMENT '棋盘状态JSON',
    player_name VARCHAR(100) COMMENT '玩家名称',
    save_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '保存时间',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成',
    INDEX idx_progress_code (progress_code)
);
```

## 初始数据

### 游戏物品初始数据
```sql
INSERT INTO game_items (name, image_path, width, height) VALUES
('坦克', '/images/tank.png', 3, 3),
('怀表', '/images/watch.png', 1, 1),
('钥匙', '/images/key.png', 1, 1),
('眼镜', '/images/glasses.png', 1, 1),
('球', '/images/ball.png', 1, 1),
('装甲车', '/images/armored_car.png', 2, 2);
```

### 默认棋盘配置
```sql
INSERT INTO game_board (name, width, height, is_default) VALUES
('默认棋盘', 10, 9, TRUE);
```

## 数据关系说明

1. **game_items** 定义了游戏中所有可用的物品类型
2. **game_board** 定义了棋盘的基本配置
3. **board_item_positions** 记录了每个物品在棋盘上的具体位置
4. **game_progress** 用于保存和恢复游戏进度

## API数据格式

### 棋盘状态JSON格式
```json
{
  "boardId": 1,
  "width": 10,
  "height": 9,
  "items": [
    {
      "id": 1,
      "itemId": 1,
      "x": 0,
      "y": 0,
      "width": 3,
      "height": 3,
      "isRemoved": false,
      "imagePath": "/images/tank.png"
    }
  ]
}
```
