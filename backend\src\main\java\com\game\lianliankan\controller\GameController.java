package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.BoardStateDTO;
import com.game.lianliankan.dto.GameProgressDTO;
import com.game.lianliankan.service.GameService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/game")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class GameController {
    
    private final GameService gameService;
    
    /**
     * 获取棋盘初始状态
     */
    @GetMapping("/board")
    public ApiResponse<BoardStateDTO> getBoardState() {
        try {
            BoardStateDTO boardState = gameService.getDefaultBoardState();
            return ApiResponse.success(boardState);
        } catch (Exception e) {
            log.error("获取棋盘状态失败", e);
            return ApiResponse.error("获取棋盘状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据版本获取棋盘状态
     */
    @GetMapping("/board/{version}")
    public ApiResponse<BoardStateDTO> getBoardStateByVersion(@PathVariable String version) {
        try {
            BoardStateDTO boardState = gameService.getBoardStateByVersion(version);
            return ApiResponse.success(boardState);
        } catch (Exception e) {
            log.error("获取版本 {} 的棋盘状态失败", version, e);
            return ApiResponse.error("获取棋盘状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可用的棋盘版本
     */
    @GetMapping("/versions")
    public ApiResponse<java.util.List<BoardStateDTO.BoardVersionDTO>> getBoardVersions() {
        try {
            java.util.List<BoardStateDTO.BoardVersionDTO> versions = gameService.getAllBoardVersions();
            return ApiResponse.success(versions);
        } catch (Exception e) {
            log.error("获取棋盘版本列表失败", e);
            return ApiResponse.error("获取棋盘版本列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存游戏进度
     */
    @PostMapping("/save")
    public ApiResponse<GameProgressDTO.SaveResponse> saveGame(@RequestBody GameProgressDTO.SaveRequest request) {
        try {
            String progressCode = gameService.saveGameProgress(request);
            GameProgressDTO.SaveResponse response = new GameProgressDTO.SaveResponse(progressCode);
            return ApiResponse.success("保存成功", response);
        } catch (Exception e) {
            log.error("保存游戏进度失败", e);
            return ApiResponse.error("保存游戏进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载游戏进度
     */
    @PostMapping("/load")
    public ApiResponse<GameProgressDTO.LoadResponse> loadGame(@RequestBody GameProgressDTO.LoadRequest request) {
        try {
            GameProgressDTO.LoadResponse response = gameService.loadGameProgress(request.getProgressCode());
            return ApiResponse.success("加载成功", response);
        } catch (Exception e) {
            log.error("加载游戏进度失败", e);
            return ApiResponse.error("加载游戏进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置游戏
     */
    @PostMapping("/reset")
    public ApiResponse<BoardStateDTO> resetGame() {
        try {
            BoardStateDTO boardState = gameService.resetGame();
            return ApiResponse.success("重置成功", boardState);
        } catch (Exception e) {
            log.error("重置游戏失败", e);
            return ApiResponse.error("重置游戏失败: " + e.getMessage());
        }
    }
}
