package com.game.lianliankan.repository;

import com.game.lianliankan.entity.GameSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GameSessionRepository extends JpaRepository<GameSession, Long> {
    
    Optional<GameSession> findBySessionId(String sessionId);
    
    List<GameSession> findByPlayerName(String playerName);
    
    List<GameSession> findByIsCompletedTrue();
    
    @Query("SELECT g FROM GameSession g WHERE g.lastUpdateTime < :cutoffTime")
    List<GameSession> findInactiveSessions(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    void deleteBySessionId(String sessionId);
}
