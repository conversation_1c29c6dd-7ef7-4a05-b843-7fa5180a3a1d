# 连连看游戏部署指南

## 环境要求

### 开发环境
- Java 21+
- Node.js 16+
- Maven 3.6+
- MySQL 8.0+

### 生产环境
- Java 21+
- MySQL 8.0+
- Nginx (可选，用于前端静态文件服务)

## 数据库配置

### 1. 安装MySQL
确保MySQL 8.0+已安装并运行

### 2. 创建数据库
```sql
-- 执行 docs/database-setup.sql 文件
mysql -u root -p < docs/database-setup.sql
```

### 3. 配置数据库连接
修改 `backend/src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: ******************************************************************************************************************
    username: your_username
    password: your_password
```

## 后端部署

### 开发环境
```bash
cd backend
mvn spring-boot:run
```

### 生产环境
```bash
cd backend
mvn clean package
java -jar target/lianliankan-backend-1.0.0.jar
```

## 前端部署

### 开发环境
```bash
cd frontend
npm install
npm run dev
```

### 生产环境
```bash
cd frontend
npm install
npm run build

# 将 dist 目录部署到 Web 服务器
# 例如 Nginx 配置:
# server {
#     listen 80;
#     server_name your-domain.com;
#     root /path/to/frontend/dist;
#     index index.html;
#     
#     location / {
#         try_files $uri $uri/ /index.html;
#     }
#     
#     location /api {
#         proxy_pass http://localhost:8080;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#     }
# }
```

## 快速启动

### 使用启动脚本 (Windows)
```bash
# 双击运行 start-dev.bat
```

### 手动启动
1. 启动MySQL数据库
2. 启动后端服务: `cd backend && mvn spring-boot:run`
3. 启动前端服务: `cd frontend && npm run dev`

## 访问地址

- 前端: http://localhost:5173
- 后端API: http://localhost:8080/api
- 数据库控制台: http://localhost:8080/api/h2-console (仅开发环境)

## 常见问题

### 1. 端口冲突
- 前端默认端口: 5173
- 后端默认端口: 8080
- 如需修改，请更新相应配置文件

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库用户名密码
- 确认数据库已创建

### 3. 跨域问题
- 后端已配置CORS支持
- 如有问题，检查 `WebConfig.java` 中的配置

### 4. 前端依赖安装失败
- 尝试删除 `node_modules` 和 `package-lock.json`
- 使用 `npm install --legacy-peer-deps`

## 性能优化建议

### 后端优化
1. 配置数据库连接池
2. 启用Redis缓存（可选）
3. 配置JVM参数

### 前端优化
1. 启用Gzip压缩
2. 配置CDN加速
3. 图片资源优化

## 监控和日志

### 后端日志
- 日志文件位置: `logs/application.log`
- 日志级别配置: `application.yml`

### 前端监控
- 浏览器开发者工具
- 网络请求监控
- 性能分析工具

## 安全配置

### 生产环境安全
1. 修改默认数据库密码
2. 配置HTTPS
3. 设置防火墙规则
4. 定期备份数据库

### API安全
- 实现用户认证（可选）
- 添加请求频率限制
- 输入验证和过滤
