{"name": "@stomp/stompjs", "version": "7.1.1", "description": "STOMP client for Javascript and Typescript", "scripts": {"clean": "rm -rf bundles esm6", "rollup": "rollup -c && rm -rf bundles/esm6/", "build": "npm run clean && npx tsc && npm run rollup && rm -rf bundles/compatibility bundles/*.d.ts && cp spec/package.json bundles && date", "test": "jasmine", "karma": "karma start spec/karma.conf.js --single-run", "lint": "tslint 'src/**/*.ts'", "prettier": "prettier --write .", "test-helpers": "tsc -p spec/helpers-src/tsconfig.json", "prepublishOnly": "npm run lint && npm run build && npm run karma && npm run test"}, "repository": {"type": "git", "url": "https://github.com/stomp-js/stompjs.git"}, "keywords": ["STOMP", "RabbitMQ", "ActiveMQ", "Websocket", "messaging", "queue", "SockJS"], "author": "<EMAIL>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/stomp-js/stompjs/issues"}, "homepage": "https://github.com/stomp-js/stompjs#readme", "devDependencies": {"@chiragrupani/karma-chromium-edge-launcher": "^2.4.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "jasmine": "^5.6.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^5.1.0", "karma-safari-launcher": "https://github.com/RLovelett/karma-safari-launcher.git#safari-webdriver", "karma-summary-reporter": "^4.0.1", "onchange": "^7.1.0", "prettier": "^3.5.3", "rollup": "^4.35.0", "text-encoding": "^0.7.0", "ts-loader": "^9.5.2", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^5.8.2", "ws": "^8.18.1"}, "type": "module", "exports": {"import": "./esm6/index.js", "require": "./bundles/stomp.umd.js"}, "main": "./bundles/stomp.umd.js", "browser": "./bundles/stomp.umd.js", "typings": "index.d.ts", "sideEffects": false}