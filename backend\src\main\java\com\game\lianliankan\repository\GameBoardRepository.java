package com.game.lianliankan.repository;

import com.game.lianliankan.entity.GameBoard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GameBoardRepository extends JpaRepository<GameBoard, Long> {

    Optional<GameBoard> findByIsDefaultTrue();

    Optional<GameBoard> findByVersion(String version);

    List<GameBoard> findByVersionOrderByIdAsc(String version);

    List<GameBoard> findAllByOrderByIdAsc();
}
