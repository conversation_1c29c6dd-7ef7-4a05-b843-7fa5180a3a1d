# 连连看游戏撤销功能实现指南

## 功能概述

为连连看游戏实现了完整的撤销功能，允许玩家撤销之前的游戏操作，提升游戏体验。

## 核心特性

### 1. 多步撤销
- 支持连续撤销多个操作
- 最多保存15步历史记录
- 实时显示可撤销步数

### 2. 智能状态管理
- 每次消除操作前自动保存游戏状态
- 使用深拷贝确保状态独立性
- 内存优化，自动清理过期历史

### 3. 用户友好界面
- 撤销按钮显示可撤销步数
- 按钮禁用状态智能管理
- 鼠标悬停显示最后操作描述
- 支持键盘快捷键 Ctrl+Z

### 4. 性能优化
- 前端本地存储，零网络延迟
- 避免频繁数据库操作
- 只在关键时机同步到服务器

## 技术实现

### 前端状态管理 (gameStore.js)

#### 新增状态属性
```javascript
state: () => ({
  // 撤销功能相关
  gameHistory: [],           // 游戏状态历史记录
  maxHistorySize: 15,        // 最大历史记录数
  canUndo: false,           // 是否可以撤销
  pendingDatabaseUpdate: false // 标记是否有待保存的更改
})
```

#### 核心方法

**保存游戏状态快照**
```javascript
saveGameState(description = '') {
  const snapshot = {
    boardState: JSON.parse(JSON.stringify(this.boardState)),
    score: this.score,
    selectedItems: [...this.selectedItems],
    timestamp: Date.now(),
    description: description
  }
  
  this.gameHistory.push(snapshot)
  
  // 限制历史记录大小
  if (this.gameHistory.length > this.maxHistorySize) {
    this.gameHistory.shift()
  }
  
  this.canUndo = this.gameHistory.length > 0
}
```

**撤销操作**
```javascript
undoLastMove() {
  if (this.gameHistory.length === 0) return false
  
  const lastState = this.gameHistory.pop()
  
  // 恢复游戏状态
  this.boardState = lastState.boardState
  this.score = lastState.score
  this.selectedItems = lastState.selectedItems
  
  // 清除动画状态
  this.connectionLine = null
  this.removingItems = []
  
  this.canUndo = this.gameHistory.length > 0
  this.pendingDatabaseUpdate = true
  
  return true
}
```

### 用户界面集成 (Game.vue)

#### 撤销按钮
```vue
<button 
  @click="undoMove" 
  class="btn-control btn-undo" 
  :disabled="!gameStore.canUndo"
  :title="gameStore.canUndo ? `撤销: ${gameStore.lastMoveDescription} (Ctrl+Z)` : '没有可撤销的操作'"
>
  <span class="undo-icon">↶</span>
  撤销 ({{ gameStore.undoStepsCount }})
</button>
```

#### 键盘快捷键支持
```javascript
const handleKeydown = (event) => {
  if (event.ctrlKey && event.key === 'z' && gameStore.canUndo) {
    event.preventDefault()
    undoMove()
  }
}
```

## 数据库同步策略

### 延迟保存机制
- **正常操作**: 只更新前端状态，不立即保存到数据库
- **撤销操作**: 完全在前端进行，不涉及数据库
- **保存时机**: 
  - 游戏完成时
  - 用户手动保存时
  - 重置游戏时
  - 导入进度时

### 优势
1. **性能**: 撤销操作零延迟
2. **用户体验**: 可以快速连续撤销
3. **网络友好**: 减少不必要的网络请求
4. **数据一致性**: 最终状态确保同步

## 使用方法

### 基本操作
1. 进行连线消除操作
2. 点击"撤销"按钮或按 Ctrl+Z
3. 游戏状态立即回到上一步

### 高级功能
- 连续撤销: 可以连续点击撤销按钮回退多步
- 状态提示: 鼠标悬停在撤销按钮上查看最后操作
- 步数显示: 按钮上实时显示可撤销步数

## 边界情况处理

### 1. 游戏开始
- 撤销按钮禁用
- 历史记录为空

### 2. 游戏完成
- 自动清除撤销历史
- 保存最终状态到数据库

### 3. 重置/导入
- 清除所有撤销历史
- 重新开始记录

### 4. 内存管理
- 限制最大历史记录数
- 自动清理最旧记录

## 测试验证

### 功能测试
1. 基本撤销: 消除物品后撤销，验证状态恢复
2. 多步撤销: 连续操作后连续撤销
3. 边界测试: 空历史撤销、历史记录限制
4. 键盘快捷键: Ctrl+Z 功能验证

### 性能测试
1. 大量操作后的撤销响应速度
2. 内存使用情况监控
3. 网络请求频率验证

## 扩展可能

### 未来功能
1. **重做功能**: 实现 Ctrl+Y 重做操作
2. **历史可视化**: 显示操作历史列表
3. **选择性撤销**: 撤销到指定步骤
4. **操作回放**: 自动回放游戏过程

### 配置选项
1. 可配置的历史记录数量
2. 可选的自动保存间隔
3. 撤销功能的开关控制

## 总结

撤销功能的实现大大提升了连连看游戏的用户体验，通过智能的状态管理和优化的数据库同步策略，在保证功能完整性的同时确保了优秀的性能表现。
