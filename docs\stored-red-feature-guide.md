# 暂存大红功能实现指南

## 功能概述

为连连看游戏添加了"暂存大红"功能，允许玩家手动记录6格、9格、12格大红的数量。这个功能纯粹用于记录，不影响游戏逻辑，但会与游戏进度一起保存和导入。

## 核心特性

### 1. 手动记录功能
- 支持记录6格、9格、12格大红数量
- 提供 + 和 - 按钮进行数量调整
- 实时显示当前数量
- 不能减少到负数

### 2. 数据持久化
- 暂存大红数据与游戏进度一起保存到数据库
- 支持进度代码导出和导入
- 游戏会话状态包含暂存大红信息
- 撤销功能也包含暂存大红状态

### 3. 用户界面
- 美观的暂存大红控制面板
- 位于游戏棋盘右侧
- 渐变背景和现代化设计
- 响应式按钮交互

## 技术实现

### 前端状态管理

#### 新增状态
```javascript
// 暂存大红功能
storedRed: {
  red6: 0,  // 6格大红数量
  red9: 0,  // 9格大红数量
  red12: 0  // 12格大红数量
}
```

#### 核心方法
```javascript
// 增加暂存大红数量
increaseStoredRed(type) {
  if (this.storedRed.hasOwnProperty(type)) {
    this.storedRed[type]++
    this.pendingDatabaseUpdate = true
  }
}

// 减少暂存大红数量
decreaseStoredRed(type) {
  if (this.storedRed.hasOwnProperty(type) && this.storedRed[type] > 0) {
    this.storedRed[type]--
    this.pendingDatabaseUpdate = true
  }
}

// 设置暂存大红数量
setStoredRed(type, value) {
  if (this.storedRed.hasOwnProperty(type) && value >= 0) {
    this.storedRed[type] = value
    this.pendingDatabaseUpdate = true
  }
}

// 清除所有暂存大红
clearStoredRed() {
  this.storedRed = { red6: 0, red9: 0, red12: 0 }
  this.pendingDatabaseUpdate = true
}
```

### 后端数据结构

#### BoardStateDTO 扩展
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoardStateDTO {
    private Long boardId;
    private Integer width;
    private Integer height;
    private List<BoardItemDTO> items;
    private StoredRedDTO storedRed; // 新增暂存大红数据
}

@Data
@NoArgsConstructor
@AllArgsConstructor
public static class StoredRedDTO {
    private Integer red6 = 0;   // 6格大红数量
    private Integer red9 = 0;   // 9格大红数量
    private Integer red12 = 0;  // 12格大红数量
}
```

### 数据同步策略

#### 保存时机
1. **手动调整暂存大红**: 标记 `pendingDatabaseUpdate = true`
2. **游戏状态更新**: 包含暂存大红数据一起保存
3. **进度导出**: 暂存大红数据包含在进度代码中
4. **会话更新**: 暂存大红数据同步到服务器

#### 恢复机制
1. **加载游戏会话**: 自动恢复暂存大红数据
2. **导入进度**: 恢复进度代码中的暂存大红数据
3. **撤销操作**: 暂存大红状态也会被撤销
4. **新游戏**: 暂存大红数据重置为0

## 用户界面设计

### 暂存大红面板
```vue
<div class="stored-red-panel">
  <div class="stored-red-header">
    <h3>暂存大红</h3>
  </div>
  <div class="stored-red-content">
    <div class="red-item">
      <span class="red-label">6格:</span>
      <button @click="gameStore.decreaseStoredRed('red6')" 
              class="btn-adjust" 
              :disabled="gameStore.storedRed.red6 <= 0">-</button>
      <span class="red-count">({{ gameStore.storedRed.red6 }})</span>
      <button @click="gameStore.increaseStoredRed('red6')" 
              class="btn-adjust">+</button>
    </div>
    <!-- 9格和12格类似 -->
  </div>
</div>
```

### 样式特点
- 渐变背景 (`linear-gradient(135deg, #667eea 0%, #764ba2 100%)`)
- 圆形调整按钮
- 悬停效果和禁用状态
- 响应式设计

## 界面布局调整

### 新布局结构
1. **游戏头部**: 玩家信息 + 返回首页按钮
2. **游戏主体**: 
   - 游戏标题
   - 棋盘和暂存大红面板（并排显示）
   - 游戏控制按钮（撤销、重置、保存、导入）
3. **其他**: 游戏完成提示、导入对话框等

### 控制按钮重新布局
- 将原本在顶部的控制按钮移到棋盘下方
- 保持撤销功能的完整性
- 添加暂存大红控制面板

## 数据流程

### 保存流程
1. 用户调整暂存大红数量
2. 前端状态立即更新
3. 标记 `pendingDatabaseUpdate = true`
4. 在适当时机（游戏保存、会话更新等）同步到数据库

### 加载流程
1. 从数据库加载游戏状态
2. 检查是否包含暂存大红数据
3. 如果有，恢复到前端状态
4. 如果没有，初始化为默认值（全部为0）

## 兼容性处理

### 向后兼容
- 旧的游戏进度代码仍然可以正常导入
- 如果没有暂存大红数据，自动初始化为0
- 不影响现有的游戏逻辑和功能

### 数据验证
- 暂存大红数量不能为负数
- 类型检查确保数据完整性
- 默认值处理避免空指针异常

## 使用方法

### 基本操作
1. **增加数量**: 点击对应格数的 "+" 按钮
2. **减少数量**: 点击对应格数的 "-" 按钮（数量为0时按钮禁用）
3. **查看数量**: 实时显示在括号中，如 "(3)"

### 数据保存
- 暂存大红数据会自动与游戏进度一起保存
- 导出进度代码时包含暂存大红信息
- 导入进度代码时自动恢复暂存大红数据

### 撤销功能
- 撤销操作会同时恢复暂存大红的历史状态
- 支持多步撤销，包括暂存大红的变化

## 总结

暂存大红功能成功集成到连连看游戏中，提供了：
- 完整的手动记录功能
- 与游戏进度的无缝集成
- 美观的用户界面
- 可靠的数据持久化
- 与撤销功能的完美配合

这个功能纯粹用于记录目的，不会影响游戏的核心逻辑，但为玩家提供了有用的数据跟踪工具。
