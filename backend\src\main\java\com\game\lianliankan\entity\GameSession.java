package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_sessions",
       indexes = {
           @Index(name = "idx_session_id", columnList = "session_id"),
           @Index(name = "idx_game_type", columnList = "game_type_id")
       })
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "session_id", nullable = false, unique = true, length = 100)
    private String sessionId;

    @Column(name = "game_type_id", nullable = false)
    private Long gameTypeId;

    @Column(name = "player_name", length = 100)
    private String playerName;

    @Column(name = "game_state", columnDefinition = "JSON")
    private String gameState;

    @Column(name = "score")
    private Integer score = 0;

    @Column(name = "level_or_stage")
    private Integer levelOrStage = 1;

    @Column(name = "is_completed")
    private Boolean isCompleted = false;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "last_update_time")
    private LocalDateTime lastUpdateTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "game_type_id", insertable = false, updatable = false)
    private GameType gameType;
    
    @PrePersist
    protected void onCreate() {
        startTime = LocalDateTime.now();
        lastUpdateTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        lastUpdateTime = LocalDateTime.now();
    }
}
