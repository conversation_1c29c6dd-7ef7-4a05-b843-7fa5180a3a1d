package com.game.lianliankan.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.game.lianliankan.dto.BoardStateDTO;
import com.game.lianliankan.dto.GameProgressDTO;
import com.game.lianliankan.dto.GameSessionDTO;
import com.game.lianliankan.entity.BoardItemPosition;
import com.game.lianliankan.entity.GameBoard;
import com.game.lianliankan.entity.GameItem;
import com.game.lianliankan.entity.GameProgress;
import com.game.lianliankan.entity.GameSession;
import com.game.lianliankan.repository.BoardItemPositionRepository;
import com.game.lianliankan.repository.GameBoardRepository;
import com.game.lianliankan.repository.GameItemRepository;
import com.game.lianliankan.repository.GameProgressRepository;
import com.game.lianliankan.repository.GameSessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GameService {
    
    private final GameBoardRepository gameBoardRepository;
    private final GameItemRepository gameItemRepository;
    private final BoardItemPositionRepository boardItemPositionRepository;
    private final GameProgressRepository gameProgressRepository;
    private final GameSessionRepository gameSessionRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * 创建新的游戏会话
     */
    @Transactional
    public GameSessionDTO.CreateResponse createNewGameSession(GameSessionDTO.CreateRequest request) {
        try {
            // 生成唯一的会话ID
            String sessionId = generateSessionId();

            // 获取默认棋盘状态
            BoardStateDTO boardState = getDefaultBoardState();
            String boardStateJson = objectMapper.writeValueAsString(boardState);

            // 创建游戏会话
            GameSession gameSession = new GameSession();
            gameSession.setSessionId(sessionId);
            gameSession.setGameTypeId(1L); // 连连看游戏类型ID为1
            gameSession.setPlayerName(request.getPlayerName());
            gameSession.setGameState(boardStateJson);
            gameSession.setIsCompleted(false);
            gameSession.setScore(0);

            gameSessionRepository.save(gameSession);

            log.info("创建新游戏会话成功，会话ID: {}, 玩家: {}", sessionId, request.getPlayerName());

            return new GameSessionDTO.CreateResponse(sessionId, boardState, gameSession.getStartTime());

        } catch (JsonProcessingException e) {
            log.error("创建游戏会话失败", e);
            throw new RuntimeException("创建游戏会话失败: " + e.getMessage());
        }
    }

    /**
     * 获取游戏会话状态
     */
    public GameSessionDTO.GameStateResponse getGameSessionState(String sessionId) {
        GameSession gameSession = gameSessionRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new RuntimeException("游戏会话不存在或已过期"));

        try {
            BoardStateDTO boardState = objectMapper.readValue(
                    gameSession.getGameState(), BoardStateDTO.class);

            return new GameSessionDTO.GameStateResponse(
                    gameSession.getSessionId(),
                    gameSession.getPlayerName(),
                    boardState,
                    gameSession.getScore(),
                    gameSession.getIsCompleted(),
                    gameSession.getStartTime(),
                    gameSession.getLastUpdateTime()
            );

        } catch (JsonProcessingException e) {
            log.error("解析游戏状态失败", e);
            throw new RuntimeException("解析游戏状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新游戏会话状态
     */
    @Transactional
    public void updateGameSessionState(GameSessionDTO.UpdateRequest request) {
        GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在或已过期"));

        try {
            String boardStateJson = objectMapper.writeValueAsString(request.getBoardState());
            gameSession.setGameState(boardStateJson);
            gameSession.setScore(request.getScore());
            gameSession.setIsCompleted(request.getIsCompleted());

            if (request.getIsCompleted()) {
                gameSession.setEndTime(LocalDateTime.now());
            }

            gameSessionRepository.save(gameSession);

            log.info("更新游戏会话状态成功，会话ID: {}", request.getSessionId());

        } catch (JsonProcessingException e) {
            log.error("更新游戏会话状态失败", e);
            throw new RuntimeException("更新游戏会话状态失败: " + e.getMessage());
        }
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "GAME_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }

    /**
     * 获取默认棋盘状态
     */
    public BoardStateDTO getDefaultBoardState() {
        // 获取默认棋盘
        GameBoard defaultBoard = gameBoardRepository.findByIsDefaultTrue()
                .orElseThrow(() -> new RuntimeException("默认棋盘不存在"));

        return getBoardStateByBoard(defaultBoard);
    }

    /**
     * 根据版本获取棋盘状态
     */
    public BoardStateDTO getBoardStateByVersion(String version) {
        // 获取指定版本的棋盘
        GameBoard board = gameBoardRepository.findByVersion(version)
                .orElseThrow(() -> new RuntimeException("版本 " + version + " 的棋盘不存在"));

        return getBoardStateByBoard(board);
    }

    /**
     * 获取所有可用的棋盘版本
     */
    public List<BoardStateDTO.BoardVersionDTO> getAllBoardVersions() {
        List<GameBoard> boards = gameBoardRepository.findAllByOrderByIdAsc();

        return boards.stream()
                .map(board -> new BoardStateDTO.BoardVersionDTO(
                        board.getVersion(),
                        board.getName(),
                        board.getWidth() + "x" + board.getHeight(),
                        board.getIsDefault()
                ))
                .sorted((a, b) -> {
                    // 自定义排序：简单版 -> 普通版 -> 困难版
                    int orderA = getVersionOrder(a.getVersion());
                    int orderB = getVersionOrder(b.getVersion());
                    return Integer.compare(orderA, orderB);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取版本排序权重
     */
    private int getVersionOrder(String version) {
        switch (version) {
            case "simple": return 1;
            case "normal": return 2;
            case "hard": return 3;
            default: return 999;
        }
    }

    /**
     * 根据棋盘对象获取棋盘状态
     */
    private BoardStateDTO getBoardStateByBoard(GameBoard board) {
        // 获取棋盘上的物品位置
        List<BoardItemPosition> positions = boardItemPositionRepository
                .findByBoardIdAndIsRemovedFalse(board.getId());

        // 转换为DTO
        List<BoardStateDTO.BoardItemDTO> items = positions.stream()
                .map(this::convertToBoardItemDTO)
                .collect(Collectors.toList());

        // 初始化暂存大红数据
        BoardStateDTO.StoredRedDTO storedRed = new BoardStateDTO.StoredRedDTO(0, 0, 0);

        return new BoardStateDTO(board.getId(), board.getWidth(),
                                board.getHeight(), items, storedRed);
    }
    
    /**
     * 保存游戏进度
     */
    @Transactional
    public String saveGameProgress(GameProgressDTO.SaveRequest request) {
        try {
            String progressCode = generateProgressCode();
            String boardStateJson = objectMapper.writeValueAsString(request.getBoardState());
            
            GameProgress gameProgress = new GameProgress();
            gameProgress.setProgressCode(progressCode);
            gameProgress.setBoardState(boardStateJson);
            gameProgress.setPlayerName(request.getPlayerName());
            gameProgress.setIsCompleted(false);
            
            gameProgressRepository.save(gameProgress);
            
            log.info("游戏进度保存成功，进度代码: {}", progressCode);
            return progressCode;
            
        } catch (JsonProcessingException e) {
            log.error("保存游戏进度失败", e);
            throw new RuntimeException("保存游戏进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载游戏进度
     */
    public GameProgressDTO.LoadResponse loadGameProgress(String progressCode) {
        GameProgress gameProgress = gameProgressRepository.findByProgressCode(progressCode)
                .orElseThrow(() -> new RuntimeException("进度代码无效或已过期"));
        
        try {
            BoardStateDTO boardState = objectMapper.readValue(
                    gameProgress.getBoardState(), BoardStateDTO.class);
            
            return new GameProgressDTO.LoadResponse(boardState, gameProgress.getSaveTime());
            
        } catch (JsonProcessingException e) {
            log.error("加载游戏进度失败", e);
            throw new RuntimeException("加载游戏进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置游戏到初始状态
     */
    public BoardStateDTO resetGame() {
        return getDefaultBoardState();
    }
    
    /**
     * 导出进度代码
     */
    @Transactional
    public GameProgressDTO.ExportResponse exportProgress(GameProgressDTO.ExportRequest request) {
        try {
            String progressCode = generateProgressCode();
            String boardStateJson = objectMapper.writeValueAsString(request.getBoardState());
            
            GameProgress gameProgress = new GameProgress();
            gameProgress.setProgressCode(progressCode);
            gameProgress.setBoardState(boardStateJson);
            gameProgress.setIsCompleted(false);
            
            gameProgressRepository.save(gameProgress);
            
            // 设置过期时间为30天后
            LocalDateTime expiryTime = LocalDateTime.now().plusDays(30);
            
            return new GameProgressDTO.ExportResponse(progressCode, expiryTime);
            
        } catch (JsonProcessingException e) {
            log.error("导出进度失败", e);
            throw new RuntimeException("导出进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入进度代码
     */
    public GameProgressDTO.LoadResponse importProgress(String progressCode) {
        return loadGameProgress(progressCode);
    }
    
    /**
     * 生成进度代码
     */
    private String generateProgressCode() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }
    
    /**
     * 转换为BoardItemDTO
     */
    private BoardStateDTO.BoardItemDTO convertToBoardItemDTO(BoardItemPosition position) {
        GameItem gameItem = position.getGameItem();
        if (gameItem == null) {
            // 如果关联查询失败，手动查询
            gameItem = gameItemRepository.findById(position.getItemId())
                    .orElseThrow(() -> new RuntimeException("游戏物品不存在: " + position.getItemId()));
        }
        
        return new BoardStateDTO.BoardItemDTO(
                position.getId(),
                gameItem.getId(),
                gameItem.getName(),
                position.getXPosition(),
                position.getYPosition(),
                gameItem.getWidth(),
                gameItem.getHeight(),
                gameItem.getImagePath(),
                position.getIsRemoved()
        );
    }
}
