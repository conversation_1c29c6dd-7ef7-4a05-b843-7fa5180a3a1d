package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_board")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameBoard {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "width", nullable = false)
    private Integer width = 10;
    
    @Column(name = "height", nullable = false)
    private Integer height = 9;
    
    @Column(name = "is_default")
    private Boolean isDefault = false;

    @Column(name = "version", length = 50)
    private String version = "normal"; // normal-普通版，simple-简单版

    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
