package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_types")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameType {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "game_code", nullable = false, unique = true, length = 50)
    private String gameCode;
    
    @Column(name = "game_name", nullable = false, length = 100)
    private String gameName;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "version", length = 20)
    private String version = "1.0.0";
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "config_schema", columnDefinition = "JSON")
    private String configSchema;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
