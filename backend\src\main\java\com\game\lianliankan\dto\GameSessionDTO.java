package com.game.lianliankan.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

public class GameSessionDTO {
    
    // 创建新游戏会话请求
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateRequest {
        private String playerName;
    }
    
    // 创建新游戏会话响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateResponse {
        private String sessionId;
        private BoardStateDTO boardState;
        private LocalDateTime startTime;
    }
    
    // 更新游戏状态请求
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateRequest {
        private String sessionId;
        private BoardStateDTO boardState;
        private Integer score;
        private Boolean isCompleted;
    }
    
    // 获取游戏状态响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameStateResponse {
        private String sessionId;
        private String playerName;
        private BoardStateDTO boardState;
        private Integer score;
        private Boolean isCompleted;
        private LocalDateTime startTime;
        private LocalDateTime lastUpdateTime;
    }
}
