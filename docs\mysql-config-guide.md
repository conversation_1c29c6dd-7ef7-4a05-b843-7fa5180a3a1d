# MySQL 多环境配置指南

## 概述

本项目支持多环境MySQL配置，包括开发环境(dev)、生产环境(prod)和测试环境(test)。

## 配置文件结构

```
backend/src/main/resources/
├── application.yml          # 主配置文件（通用配置）
├── application-dev.yml      # 开发环境配置
├── application-prod.yml     # 生产环境配置
└── application-test.yml     # 测试环境配置
```

## 配置设计理念

- **主配置文件**: 只包含通用配置，不包含环境特定的设置
- **环境配置文件**: 包含各环境的数据库、域名、跨域等特定配置
- **环境切换**: 通过 `SPRING_PROFILES_ACTIVE` 环境变量控制

## 环境配置说明

### 开发环境 (dev)
- **数据库**: `lianliankan`
- **域名**: `http://localhost:5173` (前端), `http://localhost:8080` (后端)
- **连接池**: 较小配置，适合本地开发
- **JPA**: 显示SQL，自动更新表结构
- **日志**: DEBUG级别，详细输出
- **跨域**: 允许本地开发端口

### 生产环境 (prod)
- **数据库**: `lianliankan`
- **域名**: `http://**************` (前端), `http://**************:8080` (后端)
- **连接池**: 大容量配置，支持高并发
- **JPA**: 不显示SQL，只验证表结构
- **日志**: INFO级别，记录到文件
- **安全**: 限制actuator端点暴露
- **跨域**: 只允许生产域名

### 测试环境 (test)
- **数据库**: `lianliankan_test`
- **域名**: 本地测试地址
- **连接池**: 最小配置
- **JPA**: 每次重新创建表结构
- **日志**: DEBUG级别

## 使用方法

### 1. 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp backend/.env.example backend/.env
```

### 2. 启动不同环境

#### 开发环境
```bash
# 方法1: 通过环境变量
export SPRING_PROFILES_ACTIVE=dev
java -jar target/lianliankan-backend-1.0.0.jar

# 方法2: 通过JVM参数
java -Dspring.profiles.active=dev -jar target/lianliankan-backend-1.0.0.jar

# 方法3: 通过Maven
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### 生产环境
```bash
# 设置必要的环境变量
export SPRING_PROFILES_ACTIVE=prod
export DB_PASSWORD=your_production_password
export DB_HOST=your_production_host

java -jar target/lianliankan-backend-1.0.0.jar
```

#### 测试环境
```bash
mvn test -Dspring.profiles.active=test
```

### 3. 数据库初始化

执行初始化脚本：
```sql
source backend/src/main/resources/db/init.sql
```

## 环境变量说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| SPRING_PROFILES_ACTIVE | dev | 激活的配置文件 |
| DB_HOST | localhost | 数据库主机 |
| DB_PORT | 3306 | 数据库端口 |
| DB_NAME | xiaoli | 数据库名称 |
| DB_USERNAME | xiaoli | 数据库用户名 |
| DB_PASSWORD | - | 数据库密码（生产环境必须设置） |
| JPA_DDL_AUTO | update | JPA DDL策略 |
| JPA_SHOW_SQL | false | 是否显示SQL |

## 连接池配置

### 开发环境
- 最大连接数: 10
- 最小空闲连接: 2
- 适合单人开发使用

### 生产环境
- 最大连接数: 30
- 最小空闲连接: 10
- 支持高并发访问

### 测试环境
- 最大连接数: 5
- 最小空闲连接: 1
- 最小资源占用

## 安全建议

1. **生产环境密码**: 绝不在配置文件中硬编码密码
2. **SSL连接**: 生产环境启用SSL连接
3. **权限控制**: 使用最小权限原则
4. **监控**: 启用连接池监控和泄漏检测

## 故障排除

### 常见问题

1. **连接超时**
   - 检查数据库服务是否启动
   - 验证网络连接
   - 确认防火墙设置

2. **认证失败**
   - 验证用户名密码
   - 检查用户权限
   - 确认数据库存在

3. **SSL错误**
   - 开发环境可以禁用SSL
   - 生产环境配置正确的SSL证书

### 调试方法

启用详细日志：
```yaml
logging:
  level:
    com.zaxxer.hikari: DEBUG
    org.hibernate.SQL: DEBUG
```

## 性能优化

1. **连接池调优**: 根据并发量调整连接池大小
2. **批处理**: 启用Hibernate批处理
3. **查询缓存**: 生产环境启用二级缓存
4. **索引优化**: 为常用查询添加索引
