package com.game.lianliankan.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.game.lianliankan.dto.ItemSelectionDTO;
import com.game.lianliankan.dto.BoardStateDTO;
import com.game.lianliankan.entity.GameSession;
import com.game.lianliankan.entity.GameProgress;
import com.game.lianliankan.entity.ItemSelectionItem;
import com.game.lianliankan.repository.GameSessionRepository;
import com.game.lianliankan.repository.GameProgressRepository;
import com.game.lianliankan.repository.ItemSelectionItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemSelectionService {
    
    private final ItemSelectionItemRepository itemRepository;
    private final GameSessionRepository gameSessionRepository;
    private final GameProgressRepository gameProgressRepository;
    private final ObjectMapper objectMapper;
    
    private static final Long ITEM_SELECTION_GAME_TYPE_ID = 7L;
    
    /**
     * 创建新的物品选择游戏会话
     */
    @Transactional
    public ItemSelectionDTO.CreateSessionResponse createGameSession(ItemSelectionDTO.CreateSessionRequest request) {
        try {
            // 生成会话ID
            String sessionId = UUID.randomUUID().toString();
            
            // 根据难度级别获取物品
            String difficultyLevel = request.getDifficultyLevel() != null ? request.getDifficultyLevel() : "normal";
            List<ItemSelectionItem> items = itemRepository.findActiveItemsByDifficultyOrderByPosition(difficultyLevel);
            
            // 初始化游戏状态
            List<ItemSelectionDTO.ItemInfo> itemInfos = items.stream()
                .map(item -> new ItemSelectionDTO.ItemInfo(
                    item.getId(),
                    item.getName(),
                    item.getImagePath(),
                    item.getPositionX(),
                    item.getPositionY(),
                    "default"
                ))
                .collect(Collectors.toList());
            
            // 创建游戏会话
            GameSession gameSession = new GameSession();
            gameSession.setSessionId(sessionId);
            gameSession.setGameTypeId(ITEM_SELECTION_GAME_TYPE_ID);
            gameSession.setPlayerName(request.getPlayerName());
            gameSession.setGameState(objectMapper.writeValueAsString(itemInfos));
            gameSession.setScore(0);
            gameSession.setLevelOrStage(1);
            gameSession.setIsCompleted(false);
            
            gameSessionRepository.save(gameSession);
            
            return new ItemSelectionDTO.CreateSessionResponse(sessionId, request.getPlayerName(), itemInfos);
            
        } catch (JsonProcessingException e) {
            log.error("创建游戏会话失败", e);
            throw new RuntimeException("创建游戏会话失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取游戏状态
     */
    public ItemSelectionDTO.GameStateResponse getGameState(String sessionId) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));
            
            List<ItemSelectionDTO.ItemInfo> items = objectMapper.readValue(
                gameSession.getGameState(), 
                new TypeReference<List<ItemSelectionDTO.ItemInfo>>() {}
            );
            
            // 统计状态
            long pendingCount = items.stream().filter(item -> "pending".equals(item.getStatus())).count();
            long confirmedCount = items.stream().filter(item -> "confirmed".equals(item.getStatus())).count();
            
            return new ItemSelectionDTO.GameStateResponse(
                sessionId,
                gameSession.getPlayerName(),
                items,
                (int) pendingCount,
                (int) confirmedCount,
                gameSession.getIsCompleted()
            );
            
        } catch (JsonProcessingException e) {
            log.error("获取游戏状态失败", e);
            throw new RuntimeException("获取游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新游戏状态
     */
    @Transactional
    public void updateGameState(ItemSelectionDTO.UpdateStateRequest request) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));
            
            gameSession.setGameState(objectMapper.writeValueAsString(request.getItems()));
            gameSession.setLastUpdateTime(LocalDateTime.now());
            
            gameSessionRepository.save(gameSession);
            
        } catch (JsonProcessingException e) {
            log.error("更新游戏状态失败", e);
            throw new RuntimeException("更新游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 确认选择（将所有pending状态改为confirmed）
     */
    @Transactional
    public void confirmSelection(ItemSelectionDTO.ConfirmSelectionRequest request) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));
            
            List<ItemSelectionDTO.ItemInfo> items = objectMapper.readValue(
                gameSession.getGameState(), 
                new TypeReference<List<ItemSelectionDTO.ItemInfo>>() {}
            );
            
            // 将所有pending状态改为confirmed
            items.forEach(item -> {
                if ("pending".equals(item.getStatus())) {
                    item.setStatus("confirmed");
                }
            });
            
            gameSession.setGameState(objectMapper.writeValueAsString(items));
            gameSession.setLastUpdateTime(LocalDateTime.now());
            
            gameSessionRepository.save(gameSession);
            
        } catch (JsonProcessingException e) {
            log.error("确认选择失败", e);
            throw new RuntimeException("确认选择失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置游戏（将所有状态改为default）
     */
    @Transactional
    public void resetGame(ItemSelectionDTO.ResetGameRequest request) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));
            
            List<ItemSelectionDTO.ItemInfo> items = objectMapper.readValue(
                gameSession.getGameState(), 
                new TypeReference<List<ItemSelectionDTO.ItemInfo>>() {}
            );
            
            // 将所有状态改为default
            items.forEach(item -> item.setStatus("default"));
            
            gameSession.setGameState(objectMapper.writeValueAsString(items));
            gameSession.setLastUpdateTime(LocalDateTime.now());
            gameSession.setScore(0);
            
            gameSessionRepository.save(gameSession);
            
        } catch (JsonProcessingException e) {
            log.error("重置游戏失败", e);
            throw new RuntimeException("重置游戏失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出游戏状态 - 生成可分享的进度码
     */
    @Transactional
    public ItemSelectionDTO.ExportResponse exportGameState(ItemSelectionDTO.ExportRequest request) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));

            List<ItemSelectionDTO.ItemInfo> items = objectMapper.readValue(
                gameSession.getGameState(),
                new TypeReference<List<ItemSelectionDTO.ItemInfo>>() {}
            );

            // 统计状态
            long pendingCount = items.stream().filter(item -> "pending".equals(item.getStatus())).count();
            long confirmedCount = items.stream().filter(item -> "confirmed".equals(item.getStatus())).count();

            // 生成随机进度码
            String progressCode = generateProgressCode();

            // 转换为 BoardStateDTO 格式（与连连看游戏保持一致）
            List<BoardStateDTO.BoardItemDTO> boardItems = items.stream()
                .map(item -> new BoardStateDTO.BoardItemDTO(
                    item.getId(),
                    item.getId(),
                    item.getName(),
                    item.getPositionX(),
                    item.getPositionY(),
                    1, // width
                    1, // height
                    item.getImagePath(),
                    "confirmed".equals(item.getStatus()) // isRemoved
                ))
                .collect(Collectors.toList());

            BoardStateDTO boardState = new BoardStateDTO(
                1L, // boardId
                8,  // width
                8,  // height
                boardItems,
                new BoardStateDTO.StoredRedDTO(0, 0, 0) // storedRed
            );

            // 保存到进度表，供其他玩家导入
            String boardStateJson = objectMapper.writeValueAsString(boardState);
            GameProgress gameProgress = new GameProgress();
            gameProgress.setProgressCode(progressCode);
            gameProgress.setBoardState(boardStateJson);
            gameProgress.setPlayerName(gameSession.getPlayerName());
            gameProgress.setIsCompleted(false);

            gameProgressRepository.save(gameProgress);

            return new ItemSelectionDTO.ExportResponse(
                progressCode,  // 返回进度码而不是Base64编码
                gameSession.getPlayerName(),
                (int) pendingCount,
                (int) confirmedCount
            );

        } catch (JsonProcessingException e) {
            log.error("导出游戏状态失败", e);
            throw new RuntimeException("导出游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入游戏状态 - 通过进度码导入分享的游戏状态
     */
    @Transactional
    public void importGameState(ItemSelectionDTO.ImportRequest request) {
        try {
            GameSession gameSession = gameSessionRepository.findBySessionId(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("游戏会话不存在"));

            // 通过进度码查找分享的游戏状态
            GameProgress gameProgress = gameProgressRepository.findByProgressCode(request.getImportCode())
                .orElseThrow(() -> new RuntimeException("进度码无效或已过期"));

            // 解析 BoardStateDTO 格式的数据
            BoardStateDTO boardState = objectMapper.readValue(gameProgress.getBoardState(), BoardStateDTO.class);

            // 转换回 ItemInfo 格式
            List<ItemSelectionDTO.ItemInfo> items = boardState.getItems().stream()
                .map(boardItem -> new ItemSelectionDTO.ItemInfo(
                    boardItem.getId(),
                    boardItem.getName(),
                    boardItem.getImagePath(),
                    boardItem.getX(),
                    boardItem.getY(),
                    boardItem.getIsRemoved() ? "confirmed" : "default"
                ))
                .collect(Collectors.toList());

            // 更新当前会话的游戏状态
            gameSession.setGameState(objectMapper.writeValueAsString(items));
            gameSession.setPlayerName(gameProgress.getPlayerName());
            gameSession.setLastUpdateTime(LocalDateTime.now());

            gameSessionRepository.save(gameSession);

        } catch (Exception e) {
            log.error("导入游戏状态失败", e);
            throw new RuntimeException("导入游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用的难度级别
     */
    public ItemSelectionDTO.DifficultyLevelsResponse getDifficultyLevels() {
        List<ItemSelectionDTO.DifficultyLevel> levels = Arrays.asList(
            new ItemSelectionDTO.DifficultyLevel("normal", "普通版", "标准难度，适合新手", true),
            new ItemSelectionDTO.DifficultyLevel("hard", "困难版", "更多物品，更具挑战性", false)
        );

        return new ItemSelectionDTO.DifficultyLevelsResponse(levels);
    }

    /**
     * 生成进度代码
     */
    private String generateProgressCode() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }
}
