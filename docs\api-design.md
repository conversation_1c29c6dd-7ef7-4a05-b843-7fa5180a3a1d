# API接口设计

## 基础响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

## 游戏数据管理接口

### 1. 获取棋盘初始状态
**GET** `/api/game/board`

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "boardId": 1,
    "width": 10,
    "height": 9,
    "items": [
      {
        "id": 1,
        "itemId": 1,
        "name": "坦克",
        "x": 0,
        "y": 0,
        "width": 3,
        "height": 3,
        "imagePath": "/images/tank.png",
        "isRemoved": false
      }
    ]
  }
}
```

### 2. 保存游戏进度
**POST** `/api/game/save`

**请求体:**
```json
{
  "boardState": {
    "boardId": 1,
    "items": [...]
  },
  "playerName": "玩家1"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "保存成功",
  "data": {
    "progressCode": "ABC123DEF456"
  }
}
```

### 3. 加载游戏进度
**POST** `/api/game/load`

**请求体:**
```json
{
  "progressCode": "ABC123DEF456"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "加载成功",
  "data": {
    "boardState": {
      "boardId": 1,
      "items": [...]
    },
    "saveTime": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 重置游戏
**POST** `/api/game/reset`

**响应示例:**
```json
{
  "code": 200,
  "message": "重置成功",
  "data": {
    "boardState": {
      "boardId": 1,
      "items": [...]
    }
  }
}
```

## 进度管理接口

### 1. 导出进度代码
**POST** `/api/progress/export`

**请求体:**
```json
{
  "boardState": {
    "boardId": 1,
    "items": [...]
  }
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "progressCode": "ABC123DEF456",
    "expiryTime": "2024-12-31T23:59:59Z"
  }
}
```

### 2. 导入进度代码
**POST** `/api/progress/import`

**请求体:**
```json
{
  "progressCode": "ABC123DEF456"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "boardState": {
      "boardId": 1,
      "items": [...]
    },
    "saveTime": "2024-01-01T12:00:00Z"
  }
}
```

## 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 进度代码无效 |
| 1002 | 进度代码已过期 |
| 1003 | 棋盘状态数据格式错误 |

## 游戏逻辑相关

### 连线检测算法
- 最多允许2次拐弯
- 路径上不能有其他物品阻挡
- 大物体移动时需要检测整个移动路径

### 物品消除规则
- 相同类型的物品才能连线消除
- 大物体（2格以上）需要整体移动到目标位置
- 移动过程中不能有任何障碍物
