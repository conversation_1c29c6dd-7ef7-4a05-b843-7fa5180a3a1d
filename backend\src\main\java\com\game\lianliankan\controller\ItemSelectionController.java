package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.ItemSelectionDTO;
import com.game.lianliankan.service.ItemSelectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/item-selection")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class ItemSelectionController {
    
    private final ItemSelectionService itemSelectionService;
    
    /**
     * 创建新的物品选择游戏会话
     */
    @PostMapping("/create")
    public ApiResponse<ItemSelectionDTO.CreateSessionResponse> createGameSession(
            @RequestBody ItemSelectionDTO.CreateSessionRequest request) {
        try {
            ItemSelectionDTO.CreateSessionResponse response = itemSelectionService.createGameSession(request);
            return ApiResponse.success("创建游戏会话成功", response);
        } catch (Exception e) {
            log.error("创建游戏会话失败", e);
            return ApiResponse.error("创建游戏会话失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取游戏状态
     */
    @GetMapping("/{sessionId}")
    public ApiResponse<ItemSelectionDTO.GameStateResponse> getGameState(@PathVariable String sessionId) {
        try {
            ItemSelectionDTO.GameStateResponse response = itemSelectionService.getGameState(sessionId);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取游戏状态失败", e);
            return ApiResponse.error("获取游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新游戏状态
     */
    @PostMapping("/update")
    public ApiResponse<Void> updateGameState(@RequestBody ItemSelectionDTO.UpdateStateRequest request) {
        try {
            itemSelectionService.updateGameState(request);
            return ApiResponse.success("更新游戏状态成功", null);
        } catch (Exception e) {
            log.error("更新游戏状态失败", e);
            return ApiResponse.error("更新游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 确认选择
     */
    @PostMapping("/confirm")
    public ApiResponse<Void> confirmSelection(@RequestBody ItemSelectionDTO.ConfirmSelectionRequest request) {
        try {
            itemSelectionService.confirmSelection(request);
            return ApiResponse.success("确认选择成功", null);
        } catch (Exception e) {
            log.error("确认选择失败", e);
            return ApiResponse.error("确认选择失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置游戏
     */
    @PostMapping("/reset")
    public ApiResponse<Void> resetGame(@RequestBody ItemSelectionDTO.ResetGameRequest request) {
        try {
            itemSelectionService.resetGame(request);
            return ApiResponse.success("重置游戏成功", null);
        } catch (Exception e) {
            log.error("重置游戏失败", e);
            return ApiResponse.error("重置游戏失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出游戏状态
     */
    @PostMapping("/export")
    public ApiResponse<ItemSelectionDTO.ExportResponse> exportGameState(
            @RequestBody ItemSelectionDTO.ExportRequest request) {
        try {
            ItemSelectionDTO.ExportResponse response = itemSelectionService.exportGameState(request);
            return ApiResponse.success("导出成功", response);
        } catch (Exception e) {
            log.error("导出游戏状态失败", e);
            return ApiResponse.error("导出游戏状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入游戏状态
     */
    @PostMapping("/import")
    public ApiResponse<Void> importGameState(@RequestBody ItemSelectionDTO.ImportRequest request) {
        try {
            itemSelectionService.importGameState(request);
            return ApiResponse.success("导入成功", null);
        } catch (Exception e) {
            log.error("导入游戏状态失败", e);
            return ApiResponse.error("导入游戏状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用的难度级别
     */
    @GetMapping("/difficulty-levels")
    public ApiResponse<ItemSelectionDTO.DifficultyLevelsResponse> getDifficultyLevels() {
        try {
            ItemSelectionDTO.DifficultyLevelsResponse response = itemSelectionService.getDifficultyLevels();
            return ApiResponse.success("获取难度级别成功", response);
        } catch (Exception e) {
            log.error("获取难度级别失败", e);
            return ApiResponse.error("获取难度级别失败: " + e.getMessage());
        }
    }
}
