package com.game.lianliankan.repository;

import com.game.lianliankan.entity.ItemSelectionItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemSelectionItemRepository extends JpaRepository<ItemSelectionItem, Long> {
    
    /**
     * 查找所有启用的物品，按位置排序
     */
    @Query("SELECT i FROM ItemSelectionItem i WHERE i.isActive = true ORDER BY i.positionY, i.positionX")
    List<ItemSelectionItem> findAllActiveItemsOrderByPosition();

    /**
     * 根据难度级别查找所有启用的物品，按位置排序
     */
    @Query("SELECT i FROM ItemSelectionItem i WHERE i.isActive = true AND i.difficultyLevel = :difficultyLevel ORDER BY i.positionY, i.positionX")
    List<ItemSelectionItem> findActiveItemsByDifficultyOrderByPosition(String difficultyLevel);
    
    /**
     * 根据位置查找物品
     */
    ItemSelectionItem findByPositionXAndPositionY(Integer positionX, Integer positionY);
    
    /**
     * 查找指定范围内的物品
     */
    @Query("SELECT i FROM ItemSelectionItem i WHERE i.isActive = true AND i.positionX BETWEEN :startX AND :endX AND i.positionY BETWEEN :startY AND :endY ORDER BY i.positionY, i.positionX")
    List<ItemSelectionItem> findItemsInRange(Integer startX, Integer endX, Integer startY, Integer endY);
}
