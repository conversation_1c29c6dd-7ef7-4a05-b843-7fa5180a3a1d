# 域名配置指南

本文档说明如何在前后端项目中配置和修改域名地址。

## 概述

为了方便项目部署和域名管理，我们将所有域名配置集中管理，避免在代码中硬编码域名地址。

## 后端域名配置

### 配置文件位置
- 主配置文件：`backend/src/main/resources/application.yml`
- 配置类：`backend/src/main/java/com/game/lianliankan/config/DomainConfig.java`

### 配置说明

在 `application.yml` 中：

```yaml
# 域名配置
app:
  domain:
    # 前端域名配置
    frontend:
      dev: http://localhost:5173
      prod: https://your-frontend-domain.com
    # 后端API域名配置  
    backend:
      dev: http://localhost:8080
      prod: https://your-backend-domain.com
  # 当前环境 (dev/prod)
  environment: dev
```

### 修改域名的步骤

1. **开发环境域名修改**：
   - 修改 `app.domain.frontend.dev` 和 `app.domain.backend.dev`

2. **生产环境域名修改**：
   - 修改 `app.domain.frontend.prod` 和 `app.domain.backend.prod`

3. **切换环境**：
   - 修改 `app.environment` 的值（dev 或 prod）

## 前端域名配置

### 配置文件位置
- 基础配置：`frontend/.env`
- 开发环境：`frontend/.env.development`
- 生产环境：`frontend/.env.production`
- 配置管理：`frontend/src/config/index.js`

### 环境变量说明

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `VITE_API_BASE_URL` | API基础地址 | `http://localhost:8080/api` |
| `VITE_FRONTEND_URL` | 前端域名 | `http://localhost:5173` |
| `VITE_BACKEND_URL` | 后端域名 | `http://localhost:8080` |
| `VITE_APP_NAME` | 应用名称 | `连连看游戏` |
| `VITE_APP_VERSION` | 应用版本 | `1.0.0` |

### 修改域名的步骤

1. **开发环境域名修改**：
   - 编辑 `frontend/.env.development` 文件
   - 修改相应的环境变量

2. **生产环境域名修改**：
   - 编辑 `frontend/.env.production` 文件
   - 修改相应的环境变量

## 使用示例

### 前端中使用配置

```javascript
import { config, API_BASE_URL, FRONTEND_URL } from '@/config'

// 使用API地址
console.log('API地址:', API_BASE_URL)

// 使用前端域名
console.log('前端域名:', FRONTEND_URL)

// 判断环境
if (config.isDev) {
  console.log('当前是开发环境')
}
```

### 后端中使用配置

```java
@Autowired
private DomainConfig domainConfig;

// 获取当前环境的前端域名
String frontendUrl = domainConfig.getFrontendDomain();

// 获取当前环境的后端域名
String backendUrl = domainConfig.getBackendDomain();
```

## 当前配置状态

### 生产环境配置 (已配置)
- **前端地址**: http://**************
- **后端地址**: http://**************:8080
- **API地址**: http://**************:8080/api
- **环境**: prod (生产环境)

### 快速切换工具
- `scripts/switch-to-domain.bat` - 切换到域名配置
- `scripts/switch-to-ip.bat` - 切换回IP地址配置

## 部署时的配置

### 开发环境部署
1. 确保 `app.environment` 设置为 `dev`
2. 前端使用 `npm run dev` 启动，会自动加载 `.env.development`

### 生产环境部署 (当前配置)
1. `application.yml` 中的 `app.environment` 已设置为 `prod`
2. `.env.production` 中已配置IP地址
3. 前端使用 `npm run build` 构建，会自动加载 `.env.production`

### 域名申请完成后的切换
1. 运行 `scripts/switch-to-domain.bat`
2. 按提示输入新的域名地址
3. 手动更新后端 `application.yml` 配置
4. 重启前后端服务

## 注意事项

1. **环境变量命名**：前端环境变量必须以 `VITE_` 开头才能被 Vite 识别
2. **配置优先级**：`.env.development` 和 `.env.production` 会覆盖 `.env` 中的配置
3. **安全性**：生产环境的敏感配置不要提交到版本控制系统
4. **CORS配置**：修改域名后，确保后端的CORS配置包含新的前端域名

## 常见问题

### Q: 修改域名后前端无法访问后端API
A: 检查以下几点：
1. 后端的CORS配置是否包含新的前端域名
2. 前端的API地址配置是否正确
3. 网络是否可达

### Q: 环境变量修改后不生效
A: 
1. 重启开发服务器
2. 检查环境变量名是否正确（前端必须以VITE_开头）
3. 检查配置文件语法是否正确

### Q: 如何添加新的环境（如测试环境）
A: 
1. 后端：在 `application.yml` 中添加新的环境配置
2. 前端：创建 `.env.test` 文件并配置相应变量
3. 修改构建脚本支持新环境
