package com.game.lianliankan.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoardStateDTO {

    private Long boardId;
    private Integer width;
    private Integer height;
    private List<BoardItemDTO> items;
    private StoredRedDTO storedRed; // 暂存大红数据
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BoardItemDTO {
        private Long id;
        private Long itemId;
        private String name;
        private Integer x;
        private Integer y;
        private Integer width;
        private Integer height;
        private String imagePath;
        private Boolean isRemoved;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StoredRedDTO {
        private Integer red6 = 0;   // 6格大红数量
        private Integer red9 = 0;   // 9格大红数量
        private Integer red12 = 0;  // 12格大红数量
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BoardVersionDTO {
        private String version;      // 版本标识 (normal, simple)
        private String name;         // 版本名称 (普通版棋盘, 简单版棋盘)
        private String size;         // 棋盘尺寸 (10x9, 6x6)
        private Boolean isDefault;   // 是否为默认版本
    }
}
