package com.game.lianliankan.repository;

import com.game.lianliankan.entity.GameItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GameItemRepository extends JpaRepository<GameItem, Long> {
    
    List<GameItem> findByNameContaining(String name);
    
    List<GameItem> findByWidthAndHeight(Integer width, Integer height);
}
