package com.game.lianliankan.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

public class GameProgressDTO {
    
    // 保存进度请求
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveRequest {
        private BoardStateDTO boardState;
        private String playerName;
    }
    
    // 保存进度响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveResponse {
        private String progressCode;
    }
    
    // 加载进度请求
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoadRequest {
        private String progressCode;
    }
    
    // 加载进度响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoadResponse {
        private BoardStateDTO boardState;
        private LocalDateTime saveTime;
    }
    
    // 导出进度请求
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportRequest {
        private BoardStateDTO boardState;
    }
    
    // 导出进度响应
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportResponse {
        private String progressCode;
        private LocalDateTime expiryTime;
    }
}
