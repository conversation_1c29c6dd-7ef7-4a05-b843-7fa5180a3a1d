{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../src/client.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,OAAO,EACL,eAAe,EAOf,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,GAEf,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAUzC;;;;GAIG;AACH,MAAM,OAAO,MAAM;IA8JjB;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC;IACxC,CAAC;IASD;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,IAAI,iBAAiB,CAAC,KAAmB;QACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,CAAC;IACH,CAAC;IA+BD;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IAC9D,CAAC;IAmGD;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9E,CAAC;IAID;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,MAAM,CAAC;IAC/C,CAAC;IASO,YAAY,CAAC,KAAsB;QACzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAYD;;OAEG;IACH,YAAY,OAAoB,EAAE;QA3VlC;;;;;;;;WAQG;QACI,kBAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;QAyBxC;;;WAGG;QACI,sBAAiB,GAAW,CAAC,CAAC;QAKrC;;WAEG;QACI,mBAAc,GAAW,IAAI,CAAC;QAErC;;;WAGG;QACK,wBAAmB,GAAW,CAAC,CAAC;QAExC;;;;WAIG;QACI,sBAAiB,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,mBAAmB;QAEtE;;;;;;;;;;;WAWG;QACI,sBAAiB,GAAyB,oBAAoB,CAAC,MAAM,CAAC;QAE7E;;WAEG;QACI,sBAAiB,GAAW,KAAK,CAAC;QAEzC;;WAEG;QACI,sBAAiB,GAAW,KAAK,CAAC;QAEzC;;;;;;;;;;;;;;WAcG;QACI,sBAAiB,GAAmB,cAAc,CAAC,QAAQ,CAAC;QAEnE;;;;;;;;;;;;WAYG;QACI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;;WAGG;QACI,0BAAqB,GAAW,CAAC,GAAG,IAAI,CAAC;QAEhD;;;;;;;WAOG;QACI,wBAAmB,GAAY,KAAK,CAAC;QAE5C;;;;;;;;;WASG;QACI,gCAA2B,GAAY,KAAK,CAAC;QAyJpD;;;;;;;WAOG;QACI,kCAA6B,GAAY,KAAK,CAAC;QA8BtD;;;;;WAKG;QACI,UAAK,GAAoB,eAAe,CAAC,QAAQ,CAAC;QAQvD,kBAAkB;QAClB,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,8EAA8E;QAC9E,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAE7B,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,IAAiB;QAChC,qCAAqC;QACpC,MAAc,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEnC,+CAA+C;QAC/C,IACE,IAAI,CAAC,iBAAiB,GAAG,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAC5C,CAAC;YACD,IAAI,CAAC,KAAK,CACR,+BAA+B,IAAI,CAAC,iBAAiB,oCAAoC,IAAI,CAAC,cAAc,2DAA2D,CACxK,CAAC;YACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ;QACb,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBAC3D,OAAO;YACT,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC;YAC/C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF,oEAAoE;QACpE,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,YAAY,EAAE,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1B,SAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CACR,+DAA+D,CAChE,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CACR,8DAA8D,CAC/D,CAAC;YACF,OAAO;QACT,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YAC/B,cAAc;YACd,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBACD,0DAA0D;gBAC1D,mCAAmC;gBACnC,IAAI,CAAC,KAAK,CACR,iCAAiC,IAAI,CAAC,iBAAiB,oBAAoB,CAC5E,CAAC;gBACF,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAEpC,iDAAiD;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE1C,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE;YACrD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,2BAA2B,EAAE,IAAI,CAAC,2BAA2B;YAC7D,6BAA6B,EAAE,IAAI,CAAC,6BAA6B;YAEjE,SAAS,EAAE,KAAK,CAAC,EAAE;gBACjB,sDAAsD;gBACtD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACtC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;gBACtC,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACjB,IAAI,CAAC,KAAK,CACR,sEAAsE,CACvE,CAAC;oBACF,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YACD,YAAY,EAAE,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,YAAY,EAAE,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,gBAAgB,EAAE,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,mDAAmD;gBAEnF,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,YAAY,EAAE,CAAC;oBAChD,6BAA6B;oBAC7B,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAC9C,CAAC;gBAED,qFAAqF;gBACrF,uCAAuC;gBACvC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAE3B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YACD,gBAAgB,EAAE,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,kBAAkB,EAAE,OAAO,CAAC,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YACD,kBAAkB,EAAE,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,gBAAgB,EAAE,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB;QACtB,IAAI,SAAuB,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtC,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1B,SAAS,GAAG,IAAI,SAAS,CACvB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CACtC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QACD,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CACR,qCAAqC,IAAI,CAAC,mBAAmB,IAAI,CAClE,CAAC;YAEF,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,IAAI,CAAC,iBAAiB,KAAK,oBAAoB,CAAC,WAAW,EAAE,CAAC;oBAChE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;oBAExD,iEAAiE;oBACjE,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;wBACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,CACjC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,iBAAiB,CACvB,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,KAAK,CAAC,UAAU,CAAC,UAA+B,EAAE;QACvD,MAAM,KAAK,GAAY,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QAClC,IAAI,UAAyB,CAAC;QAE9B,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACnD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEhD,2CAA2C;QAC3C,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAE7B,wCAAwC;QACxC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAChC,CAAC;QAED,IACE,IAAI,CAAC,aAAa;YAClB,mEAAmE;YACnE,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,gBAAgB,CAAC,MAAM,EACrD,CAAC;YACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACjE,wDAAwD;YACxD,UAAU,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACjD,wCAAwC;gBACxC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,GAAG,CAAC,EAAE;oBAC1C,oBAAoB,CAAC,GAAG,CAAC,CAAC;oBAC1B,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,qDAAqD;YACrD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC5C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,EAAE,gBAAgB,EAAE,CAAC;QACzC,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,wBAAwB;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACI,OAAO,CAAC,MAAsB;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACI,eAAe,CAAC,SAAiB,EAAE,QAA2B;QACnE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,SAAS,CACd,WAAmB,EACnB,QAA6B,EAC7B,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;;;OAWG;IACI,WAAW,CAAC,EAAU,EAAE,UAAwB,EAAE;QACvD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,aAAsB;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,aAAqB;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,aAAqB;QAChC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,GAAG,CACR,SAAiB,EACjB,cAAsB,EACtB,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,IAAI,CACT,SAAiB,EACjB,cAAsB,EACtB,UAAwB,EAAE;QAE1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,qFAAqF;QACrF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;CACF"}