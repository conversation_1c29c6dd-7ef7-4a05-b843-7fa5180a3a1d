# 开发环境配置
spring:
  # 开发环境数据库配置
  datasource:
    url: ***********************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      # 开发环境连接池配置（较小）
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 180000
      connection-timeout: 30000

  # JPA开发环境配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境允许自动更新表结构
    show-sql: true      # 开发环境显示SQL
    properties:
      hibernate:
        format_sql: true  # 格式化SQL便于调试

# 开发环境域名配置
app:
  domain:
    frontend:
      dev: http://localhost:5173
      prod: http://localhost:5173  # 开发环境统一使用本地
    backend:
      dev: http://localhost:8080
      prod: http://localhost:8080  # 开发环境统一使用本地
  environment: dev



# 开发环境日志配置
logging:
  level:
    com.game.lianliankan: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE  # 显示SQL参数
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 开发环境特定配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有actuator端点
  endpoint:
    health:
      show-details: always
