package com.game.lianliankan.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "game_progress", 
       indexes = @Index(name = "idx_progress_code", columnList = "progress_code"))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameProgress {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "progress_code", nullable = false, length = 500)
    private String progressCode;
    
    @Column(name = "board_state", nullable = false, columnDefinition = "TEXT")
    private String boardState;
    
    @Column(name = "player_name", length = 100)
    private String playerName;
    
    @Column(name = "save_time")
    private LocalDateTime saveTime;
    
    @Column(name = "is_completed")
    private Boolean isCompleted = false;
    
    @PrePersist
    protected void onCreate() {
        saveTime = LocalDateTime.now();
    }
}
