{"version": 3, "names": ["_index", "require", "CLEAR_KEYS", "CLEAR_KEYS_PLUS_COMMENTS", "COMMENT_KEYS", "removeProperties", "node", "opts", "map", "preserveComments", "key", "undefined", "Object", "keys", "symbols", "getOwnPropertySymbols", "sym"], "sources": ["../../src/modifications/removeProperties.ts"], "sourcesContent": ["import { COMMENT_KEYS } from \"../constants/index.ts\";\nimport type * as t from \"../index.ts\";\n\nconst CLEAR_KEYS = [\n  \"tokens\", // only exist in t.File\n  \"start\",\n  \"end\",\n  \"loc\",\n  // Fixme: should be extra.raw / extra.rawValue?\n  \"raw\",\n  \"rawValue\",\n] as const;\n\nconst CLEAR_KEYS_PLUS_COMMENTS = [\n  ...COMMENT_KEYS,\n  \"comments\",\n  ...CLEAR_KEYS,\n] as const;\n\nexport type Options = { preserveComments?: boolean };\n/**\n * Remove all of the _* properties from a node along with the additional metadata\n * properties like location data and raw token data.\n */\nexport default function removeProperties(\n  node: t.Node,\n  opts: Options = {},\n): void {\n  const map = opts.preserveComments ? CLEAR_KEYS : CLEAR_KEYS_PLUS_COMMENTS;\n  for (const key of map) {\n    // @ts-expect-error tokens only exist in t.File\n    if (node[key] != null) node[key] = undefined;\n  }\n\n  for (const key of Object.keys(node)) {\n    // @ts-expect-error string can not index node\n    if (key[0] === \"_\" && node[key] != null) node[key] = undefined;\n  }\n\n  const symbols: Array<symbol> = Object.getOwnPropertySymbols(node);\n  for (const sym of symbols) {\n    // @ts-expect-error Fixme: document symbol properties\n    node[sym] = null;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,MAAMC,UAAU,GAAG,CACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,KAAK,EAEL,KAAK,EACL,UAAU,CACF;AAEV,MAAMC,wBAAwB,GAAG,CAC/B,GAAGC,mBAAY,EACf,UAAU,EACV,GAAGF,UAAU,CACL;AAOK,SAASG,gBAAgBA,CACtCC,IAAY,EACZC,IAAa,GAAG,CAAC,CAAC,EACZ;EACN,MAAMC,GAAG,GAAGD,IAAI,CAACE,gBAAgB,GAAGP,UAAU,GAAGC,wBAAwB;EACzE,KAAK,MAAMO,GAAG,IAAIF,GAAG,EAAE;IAErB,IAAIF,IAAI,CAACI,GAAG,CAAC,IAAI,IAAI,EAAEJ,IAAI,CAACI,GAAG,CAAC,GAAGC,SAAS;EAC9C;EAEA,KAAK,MAAMD,GAAG,IAAIE,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC,EAAE;IAEnC,IAAII,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIJ,IAAI,CAACI,GAAG,CAAC,IAAI,IAAI,EAAEJ,IAAI,CAACI,GAAG,CAAC,GAAGC,SAAS;EAChE;EAEA,MAAMG,OAAsB,GAAGF,MAAM,CAACG,qBAAqB,CAACT,IAAI,CAAC;EACjE,KAAK,MAAMU,GAAG,IAAIF,OAAO,EAAE;IAEzBR,IAAI,CAACU,GAAG,CAAC,GAAG,IAAI;EAClB;AACF", "ignoreList": []}