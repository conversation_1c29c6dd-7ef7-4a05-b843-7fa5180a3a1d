{"version": 3, "file": "frame-impl.js", "sourceRoot": "", "sources": ["../src/frame-impl.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAKjC;;;;GAIG;AACH,MAAM,OAAO,SAAS;IAgBpB;;OAEG;IACH,IAAI,IAAI;QACN,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAGD;;OAEG;IACH,IAAI,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC;QACD,sDAAsD;QACtD,OAAO,IAAI,CAAC,WAAyB,CAAC;IACxC,CAAC;IAMD;;;;OAIG;IACH,YAAY,MAOX;QACC,MAAM,EACJ,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,kBAAkB,EAClB,uBAAuB,GACxB,GAAG,MAAM,CAAC;QACX,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAI,MAAc,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;QAEzD,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,IAAI,KAAK,CAAC;QACtD,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,IAAI,KAAK,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,YAAY,CACxB,QAAuB,EACvB,kBAA2B;QAE3B,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,MAAM,IAAI,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAEpE,6EAA6E;QAC7E,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEhC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5B,IACE,kBAAkB;gBAClB,QAAQ,CAAC,OAAO,KAAK,SAAS;gBAC9B,QAAQ,CAAC,OAAO,KAAK,WAAW,EAChC,CAAC;gBACD,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,SAAS,CAAC;YACnB,OAAO,EAAE,QAAQ,CAAC,OAAiB;YACnC,OAAO;YACP,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,kBAAkB;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,SAAS;QACd,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,SAAS,CAAC,YAAY,CAC3B,aAAa,EACb,IAAI,CAAC,WAAyB,CAC/B,CAAC,MAAM,CAAC;QACX,CAAC;aAAM,CAAC;YACN,OAAO,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,IACE,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,OAAO,KAAK,SAAS;gBAC1B,IAAI,CAAC,OAAO,KAAK,WAAW,EAC5B,CAAC;gBACD,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC,cAAc,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QACD,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,EACtD,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACjD,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAEO,UAAU;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,OAAO,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,UAAU,CAAC,CAAS;QACjC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAEO,MAAM,CAAC,YAAY,CACzB,aAAqB,EACrB,UAAsB;QAEtB,MAAM,kBAAkB,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACnE,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAI,UAAU,CAC/B,kBAAkB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CACtE,CAAC;QAEF,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACnC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACtD,UAAU,CAAC,GAAG,CACZ,cAAc,EACd,kBAAkB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAC9C,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC;IACD;;;;OAIG;IACI,MAAM,CAAC,QAAQ,CAAC,MAOtB;QACC,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,KAAK,CAAC,SAAS,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,GAAW;QACvC,OAAO,GAAG;aACP,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;aACtB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,GAAW;QACzC,OAAO,GAAG;aACP,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;aACrB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;aACrB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC;CACF"}