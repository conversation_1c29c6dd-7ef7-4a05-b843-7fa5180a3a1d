package com.game.lianliankan.controller;

import com.game.lianliankan.dto.ApiResponse;
import com.game.lianliankan.dto.GameProgressDTO;
import com.game.lianliankan.service.GameService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/progress")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class ProgressController {
    
    private final GameService gameService;
    
    /**
     * 导出进度代码
     */
    @PostMapping("/export")
    public ApiResponse<GameProgressDTO.ExportResponse> exportProgress(@RequestBody GameProgressDTO.ExportRequest request) {
        try {
            GameProgressDTO.ExportResponse response = gameService.exportProgress(request);
            return ApiResponse.success("导出成功", response);
        } catch (Exception e) {
            log.error("导出进度失败", e);
            return ApiResponse.error("导出进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入进度代码
     */
    @PostMapping("/import")
    public ApiResponse<GameProgressDTO.LoadResponse> importProgress(@RequestBody GameProgressDTO.LoadRequest request) {
        try {
            GameProgressDTO.LoadResponse response = gameService.importProgress(request.getProgressCode());
            return ApiResponse.success("导入成功", response);
        } catch (Exception e) {
            log.error("导入进度失败", e);
            return ApiResponse.error("导入进度失败: " + e.getMessage());
        }
    }
}
