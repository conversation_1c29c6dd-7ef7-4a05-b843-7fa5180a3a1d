(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function zo(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const fe={},an=[],nt=()=>{},sc=()=>!1,Is=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),qo=e=>e.startsWith("onUpdate:"),xe=Object.assign,Jo=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},oc=Object.prototype.hasOwnProperty,ce=(e,t)=>oc.call(e,t),$=Array.isArray,cn=e=>Wn(e)==="[object Map]",Ls=e=>Wn(e)==="[object Set]",Er=e=>Wn(e)==="[object Date]",Q=e=>typeof e=="function",Ce=e=>typeof e=="string",yt=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Wi=e=>(de(e)||Q(e))&&Q(e.then)&&Q(e.catch),zi=Object.prototype.toString,Wn=e=>zi.call(e),rc=e=>Wn(e).slice(8,-1),qi=e=>Wn(e)==="[object Object]",Xo=e=>Ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Cn=zo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ks=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ic=/-(\w)/g,Ze=ks(e=>e.replace(ic,(t,n)=>n?n.toUpperCase():"")),lc=/\B([A-Z])/g,$t=ks(e=>e.replace(lc,"-$1").toLowerCase()),Ns=ks(e=>e.charAt(0).toUpperCase()+e.slice(1)),no=ks(e=>e?`on${Ns(e)}`:""),Ut=(e,t)=>!Object.is(e,t),os=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},xo=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Co=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ac=e=>{const t=Ce(e)?Number(e):NaN;return isNaN(t)?e:t};let Rr;const Ds=()=>Rr||(Rr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Et(e){if($(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=Ce(s)?dc(s):Et(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(Ce(e)||de(e))return e}const cc=/;(?![^(]*\))/g,uc=/:([^]+)/,fc=/\/\*[^]*?\*\//g;function dc(e){const t={};return e.replace(fc,"").split(cc).forEach(n=>{if(n){const s=n.split(uc);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function mt(e){let t="";if(Ce(e))t=e;else if($(e))for(let n=0;n<e.length;n++){const s=mt(e[n]);s&&(t+=s+" ")}else if(de(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const hc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",pc=zo(hc);function Ji(e){return!!e||e===""}function mc(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Ms(e[s],t[s]);return n}function Ms(e,t){if(e===t)return!0;let n=Er(e),s=Er(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=yt(e),s=yt(t),n||s)return e===t;if(n=$(e),s=$(t),n||s)return n&&s?mc(e,t):!1;if(n=de(e),s=de(t),n||s){if(!n||!s)return!1;const o=Object.keys(e).length,r=Object.keys(t).length;if(o!==r)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Ms(e[i],t[i]))return!1}}return String(e)===String(t)}function Xi(e,t){return e.findIndex(n=>Ms(n,t))}const Yi=e=>!!(e&&e.__v_isRef===!0),ye=e=>Ce(e)?e:e==null?"":$(e)||de(e)&&(e.toString===zi||!Q(e.toString))?Yi(e)?ye(e.value):JSON.stringify(e,Qi,2):String(e),Qi=(e,t)=>Yi(t)?Qi(e,t.value):cn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[so(s,r)+" =>"]=o,n),{})}:Ls(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>so(n))}:yt(t)?so(t):de(t)&&!$(t)&&!qi(t)?String(t):t,so=(e,t="")=>{var n;return yt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ie;class Zi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ie,!t&&Ie&&(this.index=(Ie.scopes||(Ie.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ie;try{return Ie=this,t()}finally{Ie=n}}}on(){++this._on===1&&(this.prevScope=Ie,Ie=this)}off(){this._on>0&&--this._on===0&&(Ie=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function el(e){return new Zi(e)}function tl(){return Ie}function gc(e,t=!1){Ie&&Ie.cleanups.push(e)}let pe;const oo=new WeakSet;class nl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ie&&Ie.active&&Ie.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,oo.has(this)&&(oo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ol(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Tr(this),rl(this);const t=pe,n=st;pe=this,st=!0;try{return this.fn()}finally{il(this),pe=t,st=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Zo(t);this.deps=this.depsTail=void 0,Tr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?oo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Eo(this)&&this.run()}get dirty(){return Eo(this)}}let sl=0,En,Rn;function ol(e,t=!1){if(e.flags|=8,t){e.next=Rn,Rn=e;return}e.next=En,En=e}function Yo(){sl++}function Qo(){if(--sl>0)return;if(Rn){let t=Rn;for(Rn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;En;){let t=En;for(En=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function rl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function il(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Zo(s),yc(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function Eo(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ll(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ll(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Dn)||(e.globalVersion=Dn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Eo(e))))return;e.flags|=2;const t=e.dep,n=pe,s=st;pe=e,st=!0;try{rl(e);const o=e.fn(e._value);(t.version===0||Ut(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{pe=n,st=s,il(e),e.flags&=-3}}function Zo(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Zo(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function yc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let st=!0;const al=[];function Rt(){al.push(st),st=!1}function Tt(){const e=al.pop();st=e===void 0?!0:e}function Tr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=pe;pe=void 0;try{t()}finally{pe=n}}}let Dn=0;class bc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class er{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!pe||!st||pe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==pe)n=this.activeLink=new bc(pe,this),pe.deps?(n.prevDep=pe.depsTail,pe.depsTail.nextDep=n,pe.depsTail=n):pe.deps=pe.depsTail=n,cl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=pe.depsTail,n.nextDep=void 0,pe.depsTail.nextDep=n,pe.depsTail=n,pe.deps===n&&(pe.deps=s)}return n}trigger(t){this.version++,Dn++,this.notify(t)}notify(t){Yo();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Qo()}}}function cl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)cl(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ms=new WeakMap,Yt=Symbol(""),Ro=Symbol(""),Mn=Symbol("");function Le(e,t,n){if(st&&pe){let s=ms.get(e);s||ms.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new er),o.map=s,o.key=n),o.track()}}function _t(e,t,n,s,o,r){const i=ms.get(e);if(!i){Dn++;return}const l=a=>{a&&a.trigger()};if(Yo(),t==="clear")i.forEach(l);else{const a=$(e),u=a&&Xo(n);if(a&&n==="length"){const c=Number(s);i.forEach((f,h)=>{(h==="length"||h===Mn||!yt(h)&&h>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(Mn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Yt)),cn(e)&&l(i.get(Ro)));break;case"delete":a||(l(i.get(Yt)),cn(e)&&l(i.get(Ro)));break;case"set":cn(e)&&l(i.get(Yt));break}}Qo()}function vc(e,t){const n=ms.get(e);return n&&n.get(t)}function sn(e){const t=oe(e);return t===e?t:(Le(t,"iterate",Mn),Ye(e)?t:t.map(Ae))}function Fs(e){return Le(e=oe(e),"iterate",Mn),e}const Sc={__proto__:null,[Symbol.iterator](){return ro(this,Symbol.iterator,Ae)},concat(...e){return sn(this).concat(...e.map(t=>$(t)?sn(t):t))},entries(){return ro(this,"entries",e=>(e[1]=Ae(e[1]),e))},every(e,t){return bt(this,"every",e,t,void 0,arguments)},filter(e,t){return bt(this,"filter",e,t,n=>n.map(Ae),arguments)},find(e,t){return bt(this,"find",e,t,Ae,arguments)},findIndex(e,t){return bt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return bt(this,"findLast",e,t,Ae,arguments)},findLastIndex(e,t){return bt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return bt(this,"forEach",e,t,void 0,arguments)},includes(...e){return io(this,"includes",e)},indexOf(...e){return io(this,"indexOf",e)},join(e){return sn(this).join(e)},lastIndexOf(...e){return io(this,"lastIndexOf",e)},map(e,t){return bt(this,"map",e,t,void 0,arguments)},pop(){return vn(this,"pop")},push(...e){return vn(this,"push",e)},reduce(e,...t){return Ar(this,"reduce",e,t)},reduceRight(e,...t){return Ar(this,"reduceRight",e,t)},shift(){return vn(this,"shift")},some(e,t){return bt(this,"some",e,t,void 0,arguments)},splice(...e){return vn(this,"splice",e)},toReversed(){return sn(this).toReversed()},toSorted(e){return sn(this).toSorted(e)},toSpliced(...e){return sn(this).toSpliced(...e)},unshift(...e){return vn(this,"unshift",e)},values(){return ro(this,"values",Ae)}};function ro(e,t,n){const s=Fs(e),o=s[t]();return s!==e&&!Ye(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const wc=Array.prototype;function bt(e,t,n,s,o,r){const i=Fs(e),l=i!==e&&!Ye(e),a=i[t];if(a!==wc[t]){const f=a.apply(e,r);return l?Ae(f):f}let u=n;i!==e&&(l?u=function(f,h){return n.call(this,Ae(f),h,e)}:n.length>2&&(u=function(f,h){return n.call(this,f,h,e)}));const c=a.call(i,u,s);return l&&o?o(c):c}function Ar(e,t,n,s){const o=Fs(e);let r=n;return o!==e&&(Ye(e)?n.length>3&&(r=function(i,l,a){return n.call(this,i,l,a,e)}):r=function(i,l,a){return n.call(this,i,Ae(l),a,e)}),o[t](r,...s)}function io(e,t,n){const s=oe(e);Le(s,"iterate",Mn);const o=s[t](...n);return(o===-1||o===!1)&&sr(n[0])?(n[0]=oe(n[0]),s[t](...n)):o}function vn(e,t,n=[]){Rt(),Yo();const s=oe(e)[t].apply(e,n);return Qo(),Tt(),s}const _c=zo("__proto__,__v_isRef,__isVue"),ul=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(yt));function xc(e){yt(e)||(e=String(e));const t=oe(this);return Le(t,"has",e),t.hasOwnProperty(e)}class fl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?kc:ml:r?pl:hl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=$(t);if(!o){let a;if(i&&(a=Sc[n]))return a;if(n==="hasOwnProperty")return xc}const l=Reflect.get(t,n,_e(t)?t:s);return(yt(n)?ul.has(n):_c(n))||(o||Le(t,"get",n),r)?l:_e(l)?i&&Xo(n)?l:l.value:de(l)?o?yl(l):zn(l):l}}class dl extends fl{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const a=jt(r);if(!Ye(s)&&!jt(s)&&(r=oe(r),s=oe(s)),!$(t)&&_e(r)&&!_e(s))return a?!1:(r.value=s,!0)}const i=$(t)&&Xo(n)?Number(n)<t.length:ce(t,n),l=Reflect.set(t,n,s,_e(t)?t:o);return t===oe(o)&&(i?Ut(s,r)&&_t(t,"set",n,s):_t(t,"add",n,s)),l}deleteProperty(t,n){const s=ce(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&_t(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!yt(n)||!ul.has(n))&&Le(t,"has",n),s}ownKeys(t){return Le(t,"iterate",$(t)?"length":Yt),Reflect.ownKeys(t)}}class Cc extends fl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ec=new dl,Rc=new Cc,Tc=new dl(!0);const To=e=>e,es=e=>Reflect.getPrototypeOf(e);function Ac(e,t,n){return function(...s){const o=this.__v_raw,r=oe(o),i=cn(r),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=o[e](...s),c=n?To:t?gs:Ae;return!t&&Le(r,"iterate",a?Ro:Yt),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:l?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function ts(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Pc(e,t){const n={get(o){const r=this.__v_raw,i=oe(r),l=oe(o);e||(Ut(o,l)&&Le(i,"get",o),Le(i,"get",l));const{has:a}=es(i),u=t?To:e?gs:Ae;if(a.call(i,o))return u(r.get(o));if(a.call(i,l))return u(r.get(l));r!==i&&r.get(o)},get size(){const o=this.__v_raw;return!e&&Le(oe(o),"iterate",Yt),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,i=oe(r),l=oe(o);return e||(Ut(o,l)&&Le(i,"has",o),Le(i,"has",l)),o===l?r.has(o):r.has(o)||r.has(l)},forEach(o,r){const i=this,l=i.__v_raw,a=oe(l),u=t?To:e?gs:Ae;return!e&&Le(a,"iterate",Yt),l.forEach((c,f)=>o.call(r,u(c),u(f),i))}};return xe(n,e?{add:ts("add"),set:ts("set"),delete:ts("delete"),clear:ts("clear")}:{add(o){!t&&!Ye(o)&&!jt(o)&&(o=oe(o));const r=oe(this);return es(r).has.call(r,o)||(r.add(o),_t(r,"add",o,o)),this},set(o,r){!t&&!Ye(r)&&!jt(r)&&(r=oe(r));const i=oe(this),{has:l,get:a}=es(i);let u=l.call(i,o);u||(o=oe(o),u=l.call(i,o));const c=a.call(i,o);return i.set(o,r),u?Ut(r,c)&&_t(i,"set",o,r):_t(i,"add",o,r),this},delete(o){const r=oe(this),{has:i,get:l}=es(r);let a=i.call(r,o);a||(o=oe(o),a=i.call(r,o)),l&&l.call(r,o);const u=r.delete(o);return a&&_t(r,"delete",o,void 0),u},clear(){const o=oe(this),r=o.size!==0,i=o.clear();return r&&_t(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Ac(o,e,t)}),n}function tr(e,t){const n=Pc(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(ce(n,o)&&o in s?n:s,o,r)}const Oc={get:tr(!1,!1)},Ic={get:tr(!1,!0)},Lc={get:tr(!0,!1)};const hl=new WeakMap,pl=new WeakMap,ml=new WeakMap,kc=new WeakMap;function Nc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Dc(e){return e.__v_skip||!Object.isExtensible(e)?0:Nc(rc(e))}function zn(e){return jt(e)?e:nr(e,!1,Ec,Oc,hl)}function gl(e){return nr(e,!1,Tc,Ic,pl)}function yl(e){return nr(e,!0,Rc,Lc,ml)}function nr(e,t,n,s,o){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Dc(e);if(r===0)return e;const i=o.get(e);if(i)return i;const l=new Proxy(e,r===2?s:n);return o.set(e,l),l}function Bt(e){return jt(e)?Bt(e.__v_raw):!!(e&&e.__v_isReactive)}function jt(e){return!!(e&&e.__v_isReadonly)}function Ye(e){return!!(e&&e.__v_isShallow)}function sr(e){return e?!!e.__v_raw:!1}function oe(e){const t=e&&e.__v_raw;return t?oe(t):e}function or(e){return!ce(e,"__v_skip")&&Object.isExtensible(e)&&xo(e,"__v_skip",!0),e}const Ae=e=>de(e)?zn(e):e,gs=e=>de(e)?yl(e):e;function _e(e){return e?e.__v_isRef===!0:!1}function ge(e){return bl(e,!1)}function Mc(e){return bl(e,!0)}function bl(e,t){return _e(e)?e:new Fc(e,t)}class Fc{constructor(t,n){this.dep=new er,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:oe(t),this._value=n?t:Ae(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ye(t)||jt(t);t=s?t:oe(t),Ut(t,n)&&(this._rawValue=t,this._value=s?t:Ae(t),this.dep.trigger())}}function un(e){return _e(e)?e.value:e}const Hc={get:(e,t,n)=>t==="__v_raw"?e:un(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return _e(o)&&!_e(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function vl(e){return Bt(e)?e:new Proxy(e,Hc)}function Uc(e){const t=$(e)?new Array(e.length):{};for(const n in e)t[n]=jc(e,n);return t}class Bc{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return vc(oe(this._object),this._key)}}function jc(e,t,n){const s=e[t];return _e(s)?s:new Bc(e,t,n)}class Vc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new er(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Dn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&pe!==this)return ol(this,!0),!0}get value(){const t=this.dep.track();return ll(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function $c(e,t,n=!1){let s,o;return Q(e)?s=e:(s=e.get,o=e.set),new Vc(s,o,n)}const ns={},ys=new WeakMap;let zt;function Gc(e,t=!1,n=zt){if(n){let s=ys.get(n);s||ys.set(n,s=[]),s.push(e)}}function Kc(e,t,n=fe){const{immediate:s,deep:o,once:r,scheduler:i,augmentJob:l,call:a}=n,u=P=>o?P:Ye(P)||o===!1||o===0?xt(P,1):xt(P);let c,f,h,m,y=!1,S=!1;if(_e(e)?(f=()=>e.value,y=Ye(e)):Bt(e)?(f=()=>u(e),y=!0):$(e)?(S=!0,y=e.some(P=>Bt(P)||Ye(P)),f=()=>e.map(P=>{if(_e(P))return P.value;if(Bt(P))return u(P);if(Q(P))return a?a(P,2):P()})):Q(e)?t?f=a?()=>a(e,2):e:f=()=>{if(h){Rt();try{h()}finally{Tt()}}const P=zt;zt=c;try{return a?a(e,3,[m]):e(m)}finally{zt=P}}:f=nt,t&&o){const P=f,U=o===!0?1/0:o;f=()=>xt(P(),U)}const _=tl(),O=()=>{c.stop(),_&&_.active&&Jo(_.effects,c)};if(r&&t){const P=t;t=(...U)=>{P(...U),O()}}let T=S?new Array(e.length).fill(ns):ns;const C=P=>{if(!(!(c.flags&1)||!c.dirty&&!P))if(t){const U=c.run();if(o||y||(S?U.some((X,q)=>Ut(X,T[q])):Ut(U,T))){h&&h();const X=zt;zt=c;try{const q=[U,T===ns?void 0:S&&T[0]===ns?[]:T,m];T=U,a?a(t,3,q):t(...q)}finally{zt=X}}}else c.run()};return l&&l(C),c=new nl(f),c.scheduler=i?()=>i(C,!1):C,m=P=>Gc(P,!1,c),h=c.onStop=()=>{const P=ys.get(c);if(P){if(a)a(P,4);else for(const U of P)U();ys.delete(c)}},t?s?C(!0):T=c.run():i?i(C.bind(null,!0),!0):c.run(),O.pause=c.pause.bind(c),O.resume=c.resume.bind(c),O.stop=O,O}function xt(e,t=1/0,n){if(t<=0||!de(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,_e(e))xt(e.value,t,n);else if($(e))for(let s=0;s<e.length;s++)xt(e[s],t,n);else if(Ls(e)||cn(e))e.forEach(s=>{xt(s,t,n)});else if(qi(e)){for(const s in e)xt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&xt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function qn(e,t,n,s){try{return s?e(...s):e()}catch(o){Hs(o,t,n)}}function rt(e,t,n,s){if(Q(e)){const o=qn(e,t,n,s);return o&&Wi(o)&&o.catch(r=>{Hs(r,t,n)}),o}if($(e)){const o=[];for(let r=0;r<e.length;r++)o.push(rt(e[r],t,n,s));return o}}function Hs(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||fe;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(r){Rt(),qn(r,null,10,[e,a,u]),Tt();return}}Wc(e,n,o,s,i)}function Wc(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const Fe=[];let dt=-1;const fn=[];let Nt=null,rn=0;const Sl=Promise.resolve();let bs=null;function Ct(e){const t=bs||Sl;return e?t.then(this?e.bind(this):e):t}function zc(e){let t=dt+1,n=Fe.length;for(;t<n;){const s=t+n>>>1,o=Fe[s],r=Fn(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function rr(e){if(!(e.flags&1)){const t=Fn(e),n=Fe[Fe.length-1];!n||!(e.flags&2)&&t>=Fn(n)?Fe.push(e):Fe.splice(zc(t),0,e),e.flags|=1,wl()}}function wl(){bs||(bs=Sl.then(xl))}function qc(e){$(e)?fn.push(...e):Nt&&e.id===-1?Nt.splice(rn+1,0,e):e.flags&1||(fn.push(e),e.flags|=1),wl()}function Pr(e,t,n=dt+1){for(;n<Fe.length;n++){const s=Fe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Fe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function _l(e){if(fn.length){const t=[...new Set(fn)].sort((n,s)=>Fn(n)-Fn(s));if(fn.length=0,Nt){Nt.push(...t);return}for(Nt=t,rn=0;rn<Nt.length;rn++){const n=Nt[rn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Nt=null,rn=0}}const Fn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function xl(e){const t=nt;try{for(dt=0;dt<Fe.length;dt++){const n=Fe[dt];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),qn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;dt<Fe.length;dt++){const n=Fe[dt];n&&(n.flags&=-2)}dt=-1,Fe.length=0,_l(),bs=null,(Fe.length||fn.length)&&xl()}}let ze=null,Cl=null;function vs(e){const t=ze;return ze=e,Cl=e&&e.type.__scopeId||null,t}function rs(e,t=ze,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&Ur(-1);const r=vs(t);let i;try{i=e(...o)}finally{vs(r),s._d&&Ur(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Hn(e,t){if(ze===null)return e;const n=Ks(ze),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,i,l,a=fe]=t[o];r&&(Q(r)&&(r={mounted:r,updated:r}),r.deep&&xt(i),s.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Gt(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];r&&(l.oldValue=r[i].value);let a=l.dir[s];a&&(Rt(),rt(a,n,8,[e.el,l,e,t]),Tt())}}const Jc=Symbol("_vte"),El=e=>e.__isTeleport,Dt=Symbol("_leaveCb"),ss=Symbol("_enterCb");function Rl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return js(()=>{e.isMounted=!0}),Dl(()=>{e.isUnmounting=!0}),e}const Xe=[Function,Array],Tl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Xe,onEnter:Xe,onAfterEnter:Xe,onEnterCancelled:Xe,onBeforeLeave:Xe,onLeave:Xe,onAfterLeave:Xe,onLeaveCancelled:Xe,onBeforeAppear:Xe,onAppear:Xe,onAfterAppear:Xe,onAppearCancelled:Xe},Al=e=>{const t=e.subTree;return t.component?Al(t.component):t},Xc={name:"BaseTransition",props:Tl,setup(e,{slots:t}){const n=Gs(),s=Rl();return()=>{const o=t.default&&ir(t.default(),!0);if(!o||!o.length)return;const r=Pl(o),i=oe(e),{mode:l}=i;if(s.isLeaving)return lo(r);const a=Or(r);if(!a)return lo(r);let u=Un(a,i,s,n,f=>u=f);a.type!==He&&Zt(a,u);let c=n.subTree&&Or(n.subTree);if(c&&c.type!==He&&!qt(a,c)&&Al(n).type!==He){let f=Un(c,i,s,n);if(Zt(c,f),l==="out-in"&&a.type!==He)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},lo(r);l==="in-out"&&a.type!==He?f.delayLeave=(h,m,y)=>{const S=Ol(s,c);S[String(c.key)]=c,h[Dt]=()=>{m(),h[Dt]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{y(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return r}}};function Pl(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==He){t=n;break}}return t}const Yc=Xc;function Ol(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Un(e,t,n,s,o){const{appear:r,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:h,onLeave:m,onAfterLeave:y,onLeaveCancelled:S,onBeforeAppear:_,onAppear:O,onAfterAppear:T,onAppearCancelled:C}=t,P=String(e.key),U=Ol(n,e),X=(N,W)=>{N&&rt(N,s,9,W)},q=(N,W)=>{const te=W[1];X(N,W),$(N)?N.every(D=>D.length<=1)&&te():N.length<=1&&te()},K={mode:i,persisted:l,beforeEnter(N){let W=a;if(!n.isMounted)if(r)W=_||a;else return;N[Dt]&&N[Dt](!0);const te=U[P];te&&qt(e,te)&&te.el[Dt]&&te.el[Dt](),X(W,[N])},enter(N){let W=u,te=c,D=f;if(!n.isMounted)if(r)W=O||u,te=T||c,D=C||f;else return;let ne=!1;const be=N[ss]=Te=>{ne||(ne=!0,Te?X(D,[N]):X(te,[N]),K.delayedLeave&&K.delayedLeave(),N[ss]=void 0)};W?q(W,[N,be]):be()},leave(N,W){const te=String(e.key);if(N[ss]&&N[ss](!0),n.isUnmounting)return W();X(h,[N]);let D=!1;const ne=N[Dt]=be=>{D||(D=!0,W(),be?X(S,[N]):X(y,[N]),N[Dt]=void 0,U[te]===e&&delete U[te])};U[te]=e,m?q(m,[N,ne]):ne()},clone(N){const W=Un(N,t,n,s,o);return o&&o(W),W}};return K}function lo(e){if(Us(e))return e=Vt(e),e.children=null,e}function Or(e){if(!Us(e))return El(e.type)&&e.children?Pl(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Q(n.default))return n.default()}}function Zt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Zt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ir(e,t=!1,n){let s=[],o=0;for(let r=0;r<e.length;r++){let i=e[r];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:r);i.type===ve?(i.patchFlag&128&&o++,s=s.concat(ir(i.children,t,l))):(t||i.type!==He)&&s.push(l!=null?Vt(i,{key:l}):i)}if(o>1)for(let r=0;r<s.length;r++)s[r].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Il(e,t){return Q(e)?(()=>xe({name:e.name},t,{setup:e}))():e}function Ll(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Tn(e,t,n,s,o=!1){if($(e)){e.forEach((y,S)=>Tn(y,t&&($(t)?t[S]:t),n,s,o));return}if(An(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Tn(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Ks(s.component):s.el,i=o?null:r,{i:l,r:a}=e,u=t&&t.r,c=l.refs===fe?l.refs={}:l.refs,f=l.setupState,h=oe(f),m=f===fe?()=>!1:y=>ce(h,y);if(u!=null&&u!==a&&(Ce(u)?(c[u]=null,m(u)&&(f[u]=null)):_e(u)&&(u.value=null)),Q(a))qn(a,l,12,[i,c]);else{const y=Ce(a),S=_e(a);if(y||S){const _=()=>{if(e.f){const O=y?m(a)?f[a]:c[a]:a.value;o?$(O)&&Jo(O,r):$(O)?O.includes(r)||O.push(r):y?(c[a]=[r],m(a)&&(f[a]=c[a])):(a.value=[r],e.k&&(c[e.k]=a.value))}else y?(c[a]=i,m(a)&&(f[a]=i)):S&&(a.value=i,e.k&&(c[e.k]=i))};i?(_.id=-1,We(_,n)):_()}}}Ds().requestIdleCallback;Ds().cancelIdleCallback;const An=e=>!!e.type.__asyncLoader,Us=e=>e.type.__isKeepAlive;function Qc(e,t){kl(e,"a",t)}function Zc(e,t){kl(e,"da",t)}function kl(e,t,n=ke){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Bs(t,s,n),n){let o=n.parent;for(;o&&o.parent;)Us(o.parent.vnode)&&eu(s,t,n,o),o=o.parent}}function eu(e,t,n,s){const o=Bs(t,e,s,!0);lr(()=>{Jo(s[t],o)},n)}function Bs(e,t,n=ke,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{Rt();const l=Jn(n),a=rt(t,n,e,i);return l(),Tt(),a});return s?o.unshift(r):o.push(r),r}}const At=e=>(t,n=ke)=>{(!Vn||e==="sp")&&Bs(e,(...s)=>t(...s),n)},tu=At("bm"),js=At("m"),nu=At("bu"),Nl=At("u"),Dl=At("bum"),lr=At("um"),su=At("sp"),ou=At("rtg"),ru=At("rtc");function iu(e,t=ke){Bs("ec",e,t)}const Ml="components";function Ss(e,t){return au(Ml,e,!0,t)||e}const lu=Symbol.for("v-ndc");function au(e,t,n=!0,s=!1){const o=ze||ke;if(o){const r=o.type;if(e===Ml){const l=Xu(r,!1);if(l&&(l===t||l===Ze(t)||l===Ns(Ze(t))))return r}const i=Ir(o[e]||r[e],t)||Ir(o.appContext[e],t);return!i&&s?r:i}}function Ir(e,t){return e&&(e[t]||e[Ze(t)]||e[Ns(Ze(t))])}function gt(e,t,n,s){let o;const r=n&&n[s],i=$(e);if(i||Ce(e)){const l=i&&Bt(e);let a=!1,u=!1;l&&(a=!Ye(e),u=jt(e),e=Fs(e)),o=new Array(e.length);for(let c=0,f=e.length;c<f;c++)o[c]=t(a?u?gs(Ae(e[c])):Ae(e[c]):e[c],c,void 0,r&&r[c])}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,r&&r[l])}else if(de(e))if(e[Symbol.iterator])o=Array.from(e,(l,a)=>t(l,a,void 0,r&&r[a]));else{const l=Object.keys(e);o=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];o[a]=t(e[c],c,a,r&&r[a])}}else o=[];return n&&(n[s]=o),o}const Ao=e=>e?ta(e)?Ks(e):Ao(e.parent):null,Pn=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ao(e.parent),$root:e=>Ao(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ar(e),$forceUpdate:e=>e.f||(e.f=()=>{rr(e.update)}),$nextTick:e=>e.n||(e.n=Ct.bind(e.proxy)),$watch:e=>Ou.bind(e)}),ao=(e,t)=>e!==fe&&!e.__isScriptSetup&&ce(e,t),cu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(ao(s,t))return i[t]=1,s[t];if(o!==fe&&ce(o,t))return i[t]=2,o[t];if((u=e.propsOptions[0])&&ce(u,t))return i[t]=3,r[t];if(n!==fe&&ce(n,t))return i[t]=4,n[t];Po&&(i[t]=0)}}const c=Pn[t];let f,h;if(c)return t==="$attrs"&&Le(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==fe&&ce(n,t))return i[t]=4,n[t];if(h=a.config.globalProperties,ce(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return ao(o,t)?(o[t]=n,!0):s!==fe&&ce(s,t)?(s[t]=n,!0):ce(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let l;return!!n[i]||e!==fe&&ce(e,i)||ao(t,i)||(l=r[0])&&ce(l,i)||ce(s,i)||ce(Pn,i)||ce(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ce(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Lr(e){return $(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Po=!0;function uu(e){const t=ar(e),n=e.proxy,s=e.ctx;Po=!1,t.beforeCreate&&kr(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:h,beforeUpdate:m,updated:y,activated:S,deactivated:_,beforeDestroy:O,beforeUnmount:T,destroyed:C,unmounted:P,render:U,renderTracked:X,renderTriggered:q,errorCaptured:K,serverPrefetch:N,expose:W,inheritAttrs:te,components:D,directives:ne,filters:be}=t;if(u&&fu(u,s,null),i)for(const Z in i){const I=i[Z];Q(I)&&(s[Z]=I.bind(n))}if(o){const Z=o.call(n,n);de(Z)&&(e.data=zn(Z))}if(Po=!0,r)for(const Z in r){const I=r[Z],re=Q(I)?I.bind(n,n):Q(I.get)?I.get.bind(n,n):nt,Pe=!Q(I)&&Q(I.set)?I.set.bind(n):nt,$e=we({get:re,set:Pe});Object.defineProperty(s,Z,{enumerable:!0,configurable:!0,get:()=>$e.value,set:Ee=>$e.value=Ee})}if(l)for(const Z in l)Fl(l[Z],s,n,Z);if(a){const Z=Q(a)?a.call(n):a;Reflect.ownKeys(Z).forEach(I=>{is(I,Z[I])})}c&&kr(c,e,"c");function ie(Z,I){$(I)?I.forEach(re=>Z(re.bind(n))):I&&Z(I.bind(n))}if(ie(tu,f),ie(js,h),ie(nu,m),ie(Nl,y),ie(Qc,S),ie(Zc,_),ie(iu,K),ie(ru,X),ie(ou,q),ie(Dl,T),ie(lr,P),ie(su,N),$(W))if(W.length){const Z=e.exposed||(e.exposed={});W.forEach(I=>{Object.defineProperty(Z,I,{get:()=>n[I],set:re=>n[I]=re,enumerable:!0})})}else e.exposed||(e.exposed={});U&&e.render===nt&&(e.render=U),te!=null&&(e.inheritAttrs=te),D&&(e.components=D),ne&&(e.directives=ne),N&&Ll(e)}function fu(e,t,n=nt){$(e)&&(e=Oo(e));for(const s in e){const o=e[s];let r;de(o)?"default"in o?r=Qe(o.from||s,o.default,!0):r=Qe(o.from||s):r=Qe(o),_e(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[s]=r}}function kr(e,t,n){rt($(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Fl(e,t,n,s){let o=s.includes(".")?Xl(n,s):()=>n[s];if(Ce(e)){const r=t[e];Q(r)&&pt(o,r)}else if(Q(e))pt(o,e.bind(n));else if(de(e))if($(e))e.forEach(r=>Fl(r,t,n,s));else{const r=Q(e.handler)?e.handler.bind(n):t[e.handler];Q(r)&&pt(o,r,e)}}function ar(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let a;return l?a=l:!o.length&&!n&&!s?a=t:(a={},o.length&&o.forEach(u=>ws(a,u,i,!0)),ws(a,t,i)),de(t)&&r.set(t,a),a}function ws(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&ws(e,r,n,!0),o&&o.forEach(i=>ws(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=du[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const du={data:Nr,props:Dr,emits:Dr,methods:xn,computed:xn,beforeCreate:Me,created:Me,beforeMount:Me,mounted:Me,beforeUpdate:Me,updated:Me,beforeDestroy:Me,beforeUnmount:Me,destroyed:Me,unmounted:Me,activated:Me,deactivated:Me,errorCaptured:Me,serverPrefetch:Me,components:xn,directives:xn,watch:pu,provide:Nr,inject:hu};function Nr(e,t){return t?e?function(){return xe(Q(e)?e.call(this,this):e,Q(t)?t.call(this,this):t)}:t:e}function hu(e,t){return xn(Oo(e),Oo(t))}function Oo(e){if($(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Me(e,t){return e?[...new Set([].concat(e,t))]:t}function xn(e,t){return e?xe(Object.create(null),e,t):t}function Dr(e,t){return e?$(e)&&$(t)?[...new Set([...e,...t])]:xe(Object.create(null),Lr(e),Lr(t??{})):t}function pu(e,t){if(!e)return t;if(!t)return e;const n=xe(Object.create(null),e);for(const s in t)n[s]=Me(e[s],t[s]);return n}function Hl(){return{app:null,config:{isNativeTag:sc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let mu=0;function gu(e,t){return function(s,o=null){Q(s)||(s=xe({},s)),o!=null&&!de(o)&&(o=null);const r=Hl(),i=new WeakSet,l=[];let a=!1;const u=r.app={_uid:mu++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:Qu,get config(){return r.config},set config(c){},use(c,...f){return i.has(c)||(c&&Q(c.install)?(i.add(c),c.install(u,...f)):Q(c)&&(i.add(c),c(u,...f))),u},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),u},component(c,f){return f?(r.components[c]=f,u):r.components[c]},directive(c,f){return f?(r.directives[c]=f,u):r.directives[c]},mount(c,f,h){if(!a){const m=u._ceVNode||Se(s,o);return m.appContext=r,h===!0?h="svg":h===!1&&(h=void 0),f&&t?t(m,c):e(m,c,h),a=!0,u._container=c,c.__vue_app__=u,Ks(m.component)}},onUnmount(c){l.push(c)},unmount(){a&&(rt(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return r.provides[c]=f,u},runWithContext(c){const f=Qt;Qt=u;try{return c()}finally{Qt=f}}};return u}}let Qt=null;function is(e,t){if(ke){let n=ke.provides;const s=ke.parent&&ke.parent.provides;s===n&&(n=ke.provides=Object.create(s)),n[e]=t}}function Qe(e,t,n=!1){const s=Gs();if(s||Qt){let o=Qt?Qt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&Q(t)?t.call(s&&s.proxy):t}}function yu(){return!!(Gs()||Qt)}const Ul={},Bl=()=>Object.create(Ul),jl=e=>Object.getPrototypeOf(e)===Ul;function bu(e,t,n,s=!1){const o={},r=Bl();e.propsDefaults=Object.create(null),Vl(e,t,o,r);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=s?o:gl(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function vu(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,l=oe(o),[a]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(Vs(e.emitsOptions,h))continue;const m=t[h];if(a)if(ce(r,h))m!==r[h]&&(r[h]=m,u=!0);else{const y=Ze(h);o[y]=Io(a,l,y,m,e,!1)}else m!==r[h]&&(r[h]=m,u=!0)}}}else{Vl(e,t,o,r)&&(u=!0);let c;for(const f in l)(!t||!ce(t,f)&&((c=$t(f))===f||!ce(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(o[f]=Io(a,l,f,void 0,e,!0)):delete o[f]);if(r!==l)for(const f in r)(!t||!ce(t,f))&&(delete r[f],u=!0)}u&&_t(e.attrs,"set","")}function Vl(e,t,n,s){const[o,r]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(Cn(a))continue;const u=t[a];let c;o&&ce(o,c=Ze(a))?!r||!r.includes(c)?n[c]=u:(l||(l={}))[c]=u:Vs(e.emitsOptions,a)||(!(a in s)||u!==s[a])&&(s[a]=u,i=!0)}if(r){const a=oe(n),u=l||fe;for(let c=0;c<r.length;c++){const f=r[c];n[f]=Io(o,a,f,u[f],e,!ce(u,f))}}return i}function Io(e,t,n,s,o,r){const i=e[n];if(i!=null){const l=ce(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&Q(a)){const{propsDefaults:u}=o;if(n in u)s=u[n];else{const c=Jn(o);s=u[n]=a.call(null,t),c()}}else s=a;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!l?s=!1:i[1]&&(s===""||s===$t(n))&&(s=!0))}return s}const Su=new WeakMap;function $l(e,t,n=!1){const s=n?Su:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,i={},l=[];let a=!1;if(!Q(e)){const c=f=>{a=!0;const[h,m]=$l(f,t,!0);xe(i,h),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!a)return de(e)&&s.set(e,an),an;if($(r))for(let c=0;c<r.length;c++){const f=Ze(r[c]);Mr(f)&&(i[f]=fe)}else if(r)for(const c in r){const f=Ze(c);if(Mr(f)){const h=r[c],m=i[f]=$(h)||Q(h)?{type:h}:xe({},h),y=m.type;let S=!1,_=!0;if($(y))for(let O=0;O<y.length;++O){const T=y[O],C=Q(T)&&T.name;if(C==="Boolean"){S=!0;break}else C==="String"&&(_=!1)}else S=Q(y)&&y.name==="Boolean";m[0]=S,m[1]=_,(S||ce(m,"default"))&&l.push(f)}}const u=[i,l];return de(e)&&s.set(e,u),u}function Mr(e){return e[0]!=="$"&&!Cn(e)}const cr=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",ur=e=>$(e)?e.map(ht):[ht(e)],wu=(e,t,n)=>{if(t._n)return t;const s=rs((...o)=>ur(t(...o)),n);return s._c=!1,s},Gl=(e,t,n)=>{const s=e._ctx;for(const o in e){if(cr(o))continue;const r=e[o];if(Q(r))t[o]=wu(o,r,s);else if(r!=null){const i=ur(r);t[o]=()=>i}}},Kl=(e,t)=>{const n=ur(t);e.slots.default=()=>n},Wl=(e,t,n)=>{for(const s in t)(n||!cr(s))&&(e[s]=t[s])},_u=(e,t,n)=>{const s=e.slots=Bl();if(e.vnode.shapeFlag&32){const o=t.__;o&&xo(s,"__",o,!0);const r=t._;r?(Wl(s,t,n),n&&xo(s,"_",r,!0)):Gl(t,s)}else t&&Kl(e,t)},xu=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,i=fe;if(s.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:Wl(o,t,n):(r=!t.$stable,Gl(t,o)),i=t}else t&&(Kl(e,t),i={default:1});if(r)for(const l in o)!cr(l)&&i[l]==null&&delete o[l]},We=Fu;function Cu(e){return Eu(e)}function Eu(e,t){const n=Ds();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:h,setScopeId:m=nt,insertStaticContent:y}=e,S=(d,p,b,E=null,w=null,R=null,M=void 0,k=null,L=!!p.dynamicChildren)=>{if(d===p)return;d&&!qt(d,p)&&(E=x(d),Ee(d,w,R,!0),d=null),p.patchFlag===-2&&(L=!1,p.dynamicChildren=null);const{type:A,ref:G,shapeFlag:H}=p;switch(A){case $s:_(d,p,b,E);break;case He:O(d,p,b,E);break;case ls:d==null&&T(p,b,E,M);break;case ve:D(d,p,b,E,w,R,M,k,L);break;default:H&1?U(d,p,b,E,w,R,M,k,L):H&6?ne(d,p,b,E,w,R,M,k,L):(H&64||H&128)&&A.process(d,p,b,E,w,R,M,k,L,j)}G!=null&&w?Tn(G,d&&d.ref,R,p||d,!p):G==null&&d&&d.ref!=null&&Tn(d.ref,null,R,d,!0)},_=(d,p,b,E)=>{if(d==null)s(p.el=l(p.children),b,E);else{const w=p.el=d.el;p.children!==d.children&&u(w,p.children)}},O=(d,p,b,E)=>{d==null?s(p.el=a(p.children||""),b,E):p.el=d.el},T=(d,p,b,E)=>{[d.el,d.anchor]=y(d.children,p,b,E,d.el,d.anchor)},C=({el:d,anchor:p},b,E)=>{let w;for(;d&&d!==p;)w=h(d),s(d,b,E),d=w;s(p,b,E)},P=({el:d,anchor:p})=>{let b;for(;d&&d!==p;)b=h(d),o(d),d=b;o(p)},U=(d,p,b,E,w,R,M,k,L)=>{p.type==="svg"?M="svg":p.type==="math"&&(M="mathml"),d==null?X(p,b,E,w,R,M,k,L):N(d,p,w,R,M,k,L)},X=(d,p,b,E,w,R,M,k)=>{let L,A;const{props:G,shapeFlag:H,transition:V,dirs:Y}=d;if(L=d.el=i(d.type,R,G&&G.is,G),H&8?c(L,d.children):H&16&&K(d.children,L,null,E,w,co(d,R),M,k),Y&&Gt(d,null,E,"created"),q(L,d,d.scopeId,M,E),G){for(const he in G)he!=="value"&&!Cn(he)&&r(L,he,null,G[he],R,E);"value"in G&&r(L,"value",null,G.value,R),(A=G.onVnodeBeforeMount)&&ct(A,E,d)}Y&&Gt(d,null,E,"beforeMount");const se=Ru(w,V);se&&V.beforeEnter(L),s(L,p,b),((A=G&&G.onVnodeMounted)||se||Y)&&We(()=>{A&&ct(A,E,d),se&&V.enter(L),Y&&Gt(d,null,E,"mounted")},w)},q=(d,p,b,E,w)=>{if(b&&m(d,b),E)for(let R=0;R<E.length;R++)m(d,E[R]);if(w){let R=w.subTree;if(p===R||Ql(R.type)&&(R.ssContent===p||R.ssFallback===p)){const M=w.vnode;q(d,M,M.scopeId,M.slotScopeIds,w.parent)}}},K=(d,p,b,E,w,R,M,k,L=0)=>{for(let A=L;A<d.length;A++){const G=d[A]=k?Mt(d[A]):ht(d[A]);S(null,G,p,b,E,w,R,M,k)}},N=(d,p,b,E,w,R,M)=>{const k=p.el=d.el;let{patchFlag:L,dynamicChildren:A,dirs:G}=p;L|=d.patchFlag&16;const H=d.props||fe,V=p.props||fe;let Y;if(b&&Kt(b,!1),(Y=V.onVnodeBeforeUpdate)&&ct(Y,b,p,d),G&&Gt(p,d,b,"beforeUpdate"),b&&Kt(b,!0),(H.innerHTML&&V.innerHTML==null||H.textContent&&V.textContent==null)&&c(k,""),A?W(d.dynamicChildren,A,k,b,E,co(p,w),R):M||I(d,p,k,null,b,E,co(p,w),R,!1),L>0){if(L&16)te(k,H,V,b,w);else if(L&2&&H.class!==V.class&&r(k,"class",null,V.class,w),L&4&&r(k,"style",H.style,V.style,w),L&8){const se=p.dynamicProps;for(let he=0;he<se.length;he++){const ue=se[he],Be=H[ue],Oe=V[ue];(Oe!==Be||ue==="value")&&r(k,ue,Be,Oe,w,b)}}L&1&&d.children!==p.children&&c(k,p.children)}else!M&&A==null&&te(k,H,V,b,w);((Y=V.onVnodeUpdated)||G)&&We(()=>{Y&&ct(Y,b,p,d),G&&Gt(p,d,b,"updated")},E)},W=(d,p,b,E,w,R,M)=>{for(let k=0;k<p.length;k++){const L=d[k],A=p[k],G=L.el&&(L.type===ve||!qt(L,A)||L.shapeFlag&198)?f(L.el):b;S(L,A,G,null,E,w,R,M,!0)}},te=(d,p,b,E,w)=>{if(p!==b){if(p!==fe)for(const R in p)!Cn(R)&&!(R in b)&&r(d,R,p[R],null,w,E);for(const R in b){if(Cn(R))continue;const M=b[R],k=p[R];M!==k&&R!=="value"&&r(d,R,k,M,w,E)}"value"in b&&r(d,"value",p.value,b.value,w)}},D=(d,p,b,E,w,R,M,k,L)=>{const A=p.el=d?d.el:l(""),G=p.anchor=d?d.anchor:l("");let{patchFlag:H,dynamicChildren:V,slotScopeIds:Y}=p;Y&&(k=k?k.concat(Y):Y),d==null?(s(A,b,E),s(G,b,E),K(p.children||[],b,G,w,R,M,k,L)):H>0&&H&64&&V&&d.dynamicChildren?(W(d.dynamicChildren,V,b,w,R,M,k),(p.key!=null||w&&p===w.subTree)&&zl(d,p,!0)):I(d,p,b,G,w,R,M,k,L)},ne=(d,p,b,E,w,R,M,k,L)=>{p.slotScopeIds=k,d==null?p.shapeFlag&512?w.ctx.activate(p,b,E,M,L):be(p,b,E,w,R,M,L):Te(d,p,L)},be=(d,p,b,E,w,R,M)=>{const k=d.component=Ku(d,E,w);if(Us(d)&&(k.ctx.renderer=j),Wu(k,!1,M),k.asyncDep){if(w&&w.registerDep(k,ie,M),!d.el){const L=k.subTree=Se(He);O(null,L,p,b),d.placeholder=L.el}}else ie(k,d,p,b,w,R,M)},Te=(d,p,b)=>{const E=p.component=d.component;if(Du(d,p,b))if(E.asyncDep&&!E.asyncResolved){Z(E,p,b);return}else E.next=p,E.update();else p.el=d.el,E.vnode=p},ie=(d,p,b,E,w,R,M)=>{const k=()=>{if(d.isMounted){let{next:H,bu:V,u:Y,parent:se,vnode:he}=d;{const Ge=ql(d);if(Ge){H&&(H.el=he.el,Z(d,H,M)),Ge.asyncDep.then(()=>{d.isUnmounted||k()});return}}let ue=H,Be;Kt(d,!1),H?(H.el=he.el,Z(d,H,M)):H=he,V&&os(V),(Be=H.props&&H.props.onVnodeBeforeUpdate)&&ct(Be,se,H,he),Kt(d,!0);const Oe=uo(d),et=d.subTree;d.subTree=Oe,S(et,Oe,f(et.el),x(et),d,w,R),H.el=Oe.el,ue===null&&Mu(d,Oe.el),Y&&We(Y,w),(Be=H.props&&H.props.onVnodeUpdated)&&We(()=>ct(Be,se,H,he),w)}else{let H;const{el:V,props:Y}=p,{bm:se,m:he,parent:ue,root:Be,type:Oe}=d,et=An(p);if(Kt(d,!1),se&&os(se),!et&&(H=Y&&Y.onVnodeBeforeMount)&&ct(H,ue,p),Kt(d,!0),V&&me){const Ge=()=>{d.subTree=uo(d),me(V,d.subTree,d,w,null)};et&&Oe.__asyncHydrate?Oe.__asyncHydrate(V,d,Ge):Ge()}else{Be.ce&&Be.ce._def.shadowRoot!==!1&&Be.ce._injectChildStyle(Oe);const Ge=d.subTree=uo(d);S(null,Ge,b,E,d,w,R),p.el=Ge.el}if(he&&We(he,w),!et&&(H=Y&&Y.onVnodeMounted)){const Ge=p;We(()=>ct(H,ue,Ge),w)}(p.shapeFlag&256||ue&&An(ue.vnode)&&ue.vnode.shapeFlag&256)&&d.a&&We(d.a,w),d.isMounted=!0,p=b=E=null}};d.scope.on();const L=d.effect=new nl(k);d.scope.off();const A=d.update=L.run.bind(L),G=d.job=L.runIfDirty.bind(L);G.i=d,G.id=d.uid,L.scheduler=()=>rr(G),Kt(d,!0),A()},Z=(d,p,b)=>{p.component=d;const E=d.vnode.props;d.vnode=p,d.next=null,vu(d,p.props,E,b),xu(d,p.children,b),Rt(),Pr(d),Tt()},I=(d,p,b,E,w,R,M,k,L=!1)=>{const A=d&&d.children,G=d?d.shapeFlag:0,H=p.children,{patchFlag:V,shapeFlag:Y}=p;if(V>0){if(V&128){Pe(A,H,b,E,w,R,M,k,L);return}else if(V&256){re(A,H,b,E,w,R,M,k,L);return}}Y&8?(G&16&&Je(A,w,R),H!==A&&c(b,H)):G&16?Y&16?Pe(A,H,b,E,w,R,M,k,L):Je(A,w,R,!0):(G&8&&c(b,""),Y&16&&K(H,b,E,w,R,M,k,L))},re=(d,p,b,E,w,R,M,k,L)=>{d=d||an,p=p||an;const A=d.length,G=p.length,H=Math.min(A,G);let V;for(V=0;V<H;V++){const Y=p[V]=L?Mt(p[V]):ht(p[V]);S(d[V],Y,b,null,w,R,M,k,L)}A>G?Je(d,w,R,!0,!1,H):K(p,b,E,w,R,M,k,L,H)},Pe=(d,p,b,E,w,R,M,k,L)=>{let A=0;const G=p.length;let H=d.length-1,V=G-1;for(;A<=H&&A<=V;){const Y=d[A],se=p[A]=L?Mt(p[A]):ht(p[A]);if(qt(Y,se))S(Y,se,b,null,w,R,M,k,L);else break;A++}for(;A<=H&&A<=V;){const Y=d[H],se=p[V]=L?Mt(p[V]):ht(p[V]);if(qt(Y,se))S(Y,se,b,null,w,R,M,k,L);else break;H--,V--}if(A>H){if(A<=V){const Y=V+1,se=Y<G?p[Y].el:E;for(;A<=V;)S(null,p[A]=L?Mt(p[A]):ht(p[A]),b,se,w,R,M,k,L),A++}}else if(A>V)for(;A<=H;)Ee(d[A],w,R,!0),A++;else{const Y=A,se=A,he=new Map;for(A=se;A<=V;A++){const Ke=p[A]=L?Mt(p[A]):ht(p[A]);Ke.key!=null&&he.set(Ke.key,A)}let ue,Be=0;const Oe=V-se+1;let et=!1,Ge=0;const bn=new Array(Oe);for(A=0;A<Oe;A++)bn[A]=0;for(A=Y;A<=H;A++){const Ke=d[A];if(Be>=Oe){Ee(Ke,w,R,!0);continue}let at;if(Ke.key!=null)at=he.get(Ke.key);else for(ue=se;ue<=V;ue++)if(bn[ue-se]===0&&qt(Ke,p[ue])){at=ue;break}at===void 0?Ee(Ke,w,R,!0):(bn[at-se]=A+1,at>=Ge?Ge=at:et=!0,S(Ke,p[at],b,null,w,R,M,k,L),Be++)}const _r=et?Tu(bn):an;for(ue=_r.length-1,A=Oe-1;A>=0;A--){const Ke=se+A,at=p[Ke],xr=p[Ke+1],Cr=Ke+1<G?xr.el||xr.placeholder:E;bn[A]===0?S(null,at,b,Cr,w,R,M,k,L):et&&(ue<0||A!==_r[ue]?$e(at,b,Cr,2):ue--)}}},$e=(d,p,b,E,w=null)=>{const{el:R,type:M,transition:k,children:L,shapeFlag:A}=d;if(A&6){$e(d.component.subTree,p,b,E);return}if(A&128){d.suspense.move(p,b,E);return}if(A&64){M.move(d,p,b,j);return}if(M===ve){s(R,p,b);for(let H=0;H<L.length;H++)$e(L[H],p,b,E);s(d.anchor,p,b);return}if(M===ls){C(d,p,b);return}if(E!==2&&A&1&&k)if(E===0)k.beforeEnter(R),s(R,p,b),We(()=>k.enter(R),w);else{const{leave:H,delayLeave:V,afterLeave:Y}=k,se=()=>{d.ctx.isUnmounted?o(R):s(R,p,b)},he=()=>{H(R,()=>{se(),Y&&Y()})};V?V(R,se,he):he()}else s(R,p,b)},Ee=(d,p,b,E=!1,w=!1)=>{const{type:R,props:M,ref:k,children:L,dynamicChildren:A,shapeFlag:G,patchFlag:H,dirs:V,cacheIndex:Y}=d;if(H===-2&&(w=!1),k!=null&&(Rt(),Tn(k,null,b,d,!0),Tt()),Y!=null&&(p.renderCache[Y]=void 0),G&256){p.ctx.deactivate(d);return}const se=G&1&&V,he=!An(d);let ue;if(he&&(ue=M&&M.onVnodeBeforeUnmount)&&ct(ue,p,d),G&6)Zn(d.component,b,E);else{if(G&128){d.suspense.unmount(b,E);return}se&&Gt(d,null,p,"beforeUnmount"),G&64?d.type.remove(d,p,b,j,E):A&&!A.hasOnce&&(R!==ve||H>0&&H&64)?Je(A,p,b,!1,!0):(R===ve&&H&384||!w&&G&16)&&Je(L,p,b),E&&Ue(d)}(he&&(ue=M&&M.onVnodeUnmounted)||se)&&We(()=>{ue&&ct(ue,p,d),se&&Gt(d,null,p,"unmounted")},b)},Ue=d=>{const{type:p,el:b,anchor:E,transition:w}=d;if(p===ve){nn(b,E);return}if(p===ls){P(d);return}const R=()=>{o(b),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(d.shapeFlag&1&&w&&!w.persisted){const{leave:M,delayLeave:k}=w,L=()=>M(b,R);k?k(d.el,R,L):L()}else R()},nn=(d,p)=>{let b;for(;d!==p;)b=h(d),o(d),d=b;o(p)},Zn=(d,p,b)=>{const{bum:E,scope:w,job:R,subTree:M,um:k,m:L,a:A,parent:G,slots:{__:H}}=d;Fr(L),Fr(A),E&&os(E),G&&$(H)&&H.forEach(V=>{G.renderCache[V]=void 0}),w.stop(),R&&(R.flags|=8,Ee(M,d,p,b)),k&&We(k,p),We(()=>{d.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Je=(d,p,b,E=!1,w=!1,R=0)=>{for(let M=R;M<d.length;M++)Ee(d[M],p,b,E,w)},x=d=>{if(d.shapeFlag&6)return x(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const p=h(d.anchor||d.el),b=p&&p[Jc];return b?h(b):p};let B=!1;const F=(d,p,b)=>{d==null?p._vnode&&Ee(p._vnode,null,null,!0):S(p._vnode||null,d,p,null,null,null,b),p._vnode=d,B||(B=!0,Pr(),_l(),B=!1)},j={p:S,um:Ee,m:$e,r:Ue,mt:be,mc:K,pc:I,pbc:W,n:x,o:e};let le,me;return t&&([le,me]=t(j)),{render:F,hydrate:le,createApp:gu(F,le)}}function co({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Kt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ru(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function zl(e,t,n=!1){const s=e.children,o=t.children;if($(s)&&$(o))for(let r=0;r<s.length;r++){const i=s[r];let l=o[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[r]=Mt(o[r]),l.el=i.el),!n&&l.patchFlag!==-2&&zl(i,l)),l.type===$s&&(l.el=i.el),l.type===He&&!l.el&&(l.el=i.el)}}function Tu(e){const t=e.slice(),n=[0];let s,o,r,i,l;const a=e.length;for(s=0;s<a;s++){const u=e[s];if(u!==0){if(o=n[n.length-1],e[o]<u){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<u?r=l+1:i=l;u<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function ql(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ql(t)}function Fr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Au=Symbol.for("v-scx"),Pu=()=>Qe(Au);function pt(e,t,n){return Jl(e,t,n)}function Jl(e,t,n=fe){const{immediate:s,deep:o,flush:r,once:i}=n,l=xe({},n),a=t&&s||!t&&r!=="post";let u;if(Vn){if(r==="sync"){const m=Pu();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=nt,m.resume=nt,m.pause=nt,m}}const c=ke;l.call=(m,y,S)=>rt(m,c,y,S);let f=!1;r==="post"?l.scheduler=m=>{We(m,c&&c.suspense)}:r!=="sync"&&(f=!0,l.scheduler=(m,y)=>{y?m():rr(m)}),l.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const h=Kc(e,t,l);return Vn&&(u?u.push(h):a&&h()),h}function Ou(e,t,n){const s=this.proxy,o=Ce(e)?e.includes(".")?Xl(s,e):()=>s[e]:e.bind(s,s);let r;Q(t)?r=t:(r=t.handler,n=t);const i=Jn(this),l=Jl(o,r.bind(s),n);return i(),l}function Xl(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Iu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ze(t)}Modifiers`]||e[`${$t(t)}Modifiers`];function Lu(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||fe;let o=n;const r=t.startsWith("update:"),i=r&&Iu(s,t.slice(7));i&&(i.trim&&(o=n.map(c=>Ce(c)?c.trim():c)),i.number&&(o=n.map(Co)));let l,a=s[l=no(t)]||s[l=no(Ze(t))];!a&&r&&(a=s[l=no($t(t))]),a&&rt(a,e,6,o);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,rt(u,e,6,o)}}function Yl(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let i={},l=!1;if(!Q(e)){const a=u=>{const c=Yl(u,t,!0);c&&(l=!0,xe(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!r&&!l?(de(e)&&s.set(e,null),null):($(r)?r.forEach(a=>i[a]=null):xe(i,r),de(e)&&s.set(e,i),i)}function Vs(e,t){return!e||!Is(t)?!1:(t=t.slice(2).replace(/Once$/,""),ce(e,t[0].toLowerCase()+t.slice(1))||ce(e,$t(t))||ce(e,t))}function uo(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:h,setupState:m,ctx:y,inheritAttrs:S}=e,_=vs(e);let O,T;try{if(n.shapeFlag&4){const P=o||s,U=P;O=ht(u.call(U,P,c,f,m,h,y)),T=l}else{const P=t;O=ht(P.length>1?P(f,{attrs:l,slots:i,emit:a}):P(f,null)),T=t.props?l:ku(l)}}catch(P){On.length=0,Hs(P,e,1),O=Se(He)}let C=O;if(T&&S!==!1){const P=Object.keys(T),{shapeFlag:U}=C;P.length&&U&7&&(r&&P.some(qo)&&(T=Nu(T,r)),C=Vt(C,T,!1,!0))}return n.dirs&&(C=Vt(C,null,!1,!0),C.dirs=C.dirs?C.dirs.concat(n.dirs):n.dirs),n.transition&&Zt(C,n.transition),O=C,vs(_),O}const ku=e=>{let t;for(const n in e)(n==="class"||n==="style"||Is(n))&&((t||(t={}))[n]=e[n]);return t},Nu=(e,t)=>{const n={};for(const s in e)(!qo(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Du(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:l,patchFlag:a}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?Hr(s,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(i[h]!==s[h]&&!Vs(u,h))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Hr(s,i,u):!0:!!i;return!1}function Hr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!Vs(n,r))return!0}return!1}function Mu({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ql=e=>e.__isSuspense;function Fu(e,t){t&&t.pendingBranch?$(e)?t.effects.push(...e):t.effects.push(e):qc(e)}const ve=Symbol.for("v-fgt"),$s=Symbol.for("v-txt"),He=Symbol.for("v-cmt"),ls=Symbol.for("v-stc"),On=[];let qe=null;function z(e=!1){On.push(qe=e?null:[])}function Hu(){On.pop(),qe=On[On.length-1]||null}let Bn=1;function Ur(e,t=!1){Bn+=e,e<0&&qe&&t&&(qe.hasOnce=!0)}function Zl(e){return e.dynamicChildren=Bn>0?qe||an:null,Hu(),Bn>0&&qe&&qe.push(e),e}function J(e,t,n,s,o,r){return Zl(g(e,t,n,s,o,r,!0))}function Uu(e,t,n,s,o){return Zl(Se(e,t,n,s,o,!0))}function _s(e){return e?e.__v_isVNode===!0:!1}function qt(e,t){return e.type===t.type&&e.key===t.key}const ea=({key:e})=>e??null,as=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ce(e)||_e(e)||Q(e)?{i:ze,r:e,k:t,f:!!n}:e:null);function g(e,t=null,n=null,s=0,o=null,r=e===ve?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ea(t),ref:t&&as(t),scopeId:Cl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:ze};return l?(fr(a,n),r&128&&e.normalize(a)):n&&(a.shapeFlag|=Ce(n)?8:16),Bn>0&&!i&&qe&&(a.patchFlag>0||r&6)&&a.patchFlag!==32&&qe.push(a),a}const Se=Bu;function Bu(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===lu)&&(e=He),_s(e)){const l=Vt(e,t,!0);return n&&fr(l,n),Bn>0&&!r&&qe&&(l.shapeFlag&6?qe[qe.indexOf(e)]=l:qe.push(l)),l.patchFlag=-2,l}if(Yu(e)&&(e=e.__vccOpts),t){t=ju(t);let{class:l,style:a}=t;l&&!Ce(l)&&(t.class=mt(l)),de(a)&&(sr(a)&&!$(a)&&(a=xe({},a)),t.style=Et(a))}const i=Ce(e)?1:Ql(e)?128:El(e)?64:de(e)?4:Q(e)?2:0;return g(e,t,n,s,o,i,r,!0)}function ju(e){return e?sr(e)||jl(e)?xe({},e):e:null}function Vt(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:l,transition:a}=e,u=t?Vu(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&ea(u),ref:t&&t.ref?n&&r?$(r)?r.concat(as(t)):[r,as(t)]:as(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ve?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&Zt(c,a.clone(c)),c}function jn(e=" ",t=0){return Se($s,null,e,t)}function cs(e,t){const n=Se(ls,null,e);return n.staticCount=t,n}function De(e="",t=!1){return t?(z(),Uu(He,null,e)):Se(He,null,e)}function ht(e){return e==null||typeof e=="boolean"?Se(He):$(e)?Se(ve,null,e.slice()):_s(e)?Mt(e):Se($s,null,String(e))}function Mt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function fr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if($(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),fr(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!jl(t)?t._ctx=ze:o===3&&ze&&(ze.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Q(t)?(t={default:t,_ctx:ze},n=32):(t=String(t),s&64?(n=16,t=[jn(t)]):n=8);e.children=t,e.shapeFlag|=n}function Vu(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=mt([t.class,s.class]));else if(o==="style")t.style=Et([t.style,s.style]);else if(Is(o)){const r=t[o],i=s[o];i&&r!==i&&!($(r)&&r.includes(i))&&(t[o]=r?[].concat(r,i):i)}else o!==""&&(t[o]=s[o])}return t}function ct(e,t,n,s=null){rt(e,t,7,[n,s])}const $u=Hl();let Gu=0;function Ku(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||$u,r={uid:Gu++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Zi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:$l(s,o),emitsOptions:Yl(s,o),emit:null,emitted:null,propsDefaults:fe,inheritAttrs:s.inheritAttrs,ctx:fe,data:fe,props:fe,attrs:fe,slots:fe,refs:fe,setupState:fe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Lu.bind(null,r),e.ce&&e.ce(r),r}let ke=null;const Gs=()=>ke||ze;let xs,Lo;{const e=Ds(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(i=>i(r)):o[0](r)}};xs=t("__VUE_INSTANCE_SETTERS__",n=>ke=n),Lo=t("__VUE_SSR_SETTERS__",n=>Vn=n)}const Jn=e=>{const t=ke;return xs(e),e.scope.on(),()=>{e.scope.off(),xs(t)}},Br=()=>{ke&&ke.scope.off(),xs(null)};function ta(e){return e.vnode.shapeFlag&4}let Vn=!1;function Wu(e,t=!1,n=!1){t&&Lo(t);const{props:s,children:o}=e.vnode,r=ta(e);bu(e,s,r,t),_u(e,o,n||t);const i=r?zu(e,t):void 0;return t&&Lo(!1),i}function zu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,cu);const{setup:s}=n;if(s){Rt();const o=e.setupContext=s.length>1?Ju(e):null,r=Jn(e),i=qn(s,e,0,[e.props,o]),l=Wi(i);if(Tt(),r(),(l||e.sp)&&!An(e)&&Ll(e),l){if(i.then(Br,Br),t)return i.then(a=>{jr(e,a,t)}).catch(a=>{Hs(a,e,0)});e.asyncDep=i}else jr(e,i,t)}else na(e,t)}function jr(e,t,n){Q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.setupState=vl(t)),na(e,n)}let Vr;function na(e,t,n){const s=e.type;if(!e.render){if(!t&&Vr&&!s.render){const o=s.template||ar(e).template;if(o){const{isCustomElement:r,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:a}=s,u=xe(xe({isCustomElement:r,delimiters:l},i),a);s.render=Vr(o,u)}}e.render=s.render||nt}{const o=Jn(e);Rt();try{uu(e)}finally{Tt(),o()}}}const qu={get(e,t){return Le(e,"get",""),e[t]}};function Ju(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,qu),slots:e.slots,emit:e.emit,expose:t}}function Ks(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(vl(or(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Pn)return Pn[n](e)},has(t,n){return n in t||n in Pn}})):e.proxy}function Xu(e,t=!0){return Q(e)?e.displayName||e.name:e.name||t&&e.__name}function Yu(e){return Q(e)&&"__vccOpts"in e}const we=(e,t)=>$c(e,t,Vn);function dr(e,t,n){const s=arguments.length;return s===2?de(t)&&!$(t)?_s(t)?Se(e,null,[t]):Se(e,t):Se(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&_s(n)&&(n=[n]),Se(e,t,n))}const Qu="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ko;const $r=typeof window<"u"&&window.trustedTypes;if($r)try{ko=$r.createPolicy("vue",{createHTML:e=>e})}catch{}const sa=ko?e=>ko.createHTML(e):e=>e,Zu="http://www.w3.org/2000/svg",ef="http://www.w3.org/1998/Math/MathML",wt=typeof document<"u"?document:null,Gr=wt&&wt.createElement("template"),tf={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?wt.createElementNS(Zu,e):t==="mathml"?wt.createElementNS(ef,e):n?wt.createElement(e,{is:n}):wt.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>wt.createTextNode(e),createComment:e=>wt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>wt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{Gr.innerHTML=sa(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Gr.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Pt="transition",Sn="animation",hn=Symbol("_vtc"),oa={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},ra=xe({},Tl,oa),nf=e=>(e.displayName="Transition",e.props=ra,e),Kr=nf((e,{slots:t})=>dr(Yc,ia(e),t)),Wt=(e,t=[])=>{$(e)?e.forEach(n=>n(...t)):e&&e(...t)},Wr=e=>e?$(e)?e.some(t=>t.length>1):e.length>1:!1;function ia(e){const t={};for(const D in e)D in oa||(t[D]=e[D]);if(e.css===!1)return t;const{name:n="v",type:s,duration:o,enterFromClass:r=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=r,appearActiveClass:u=i,appearToClass:c=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,y=sf(o),S=y&&y[0],_=y&&y[1],{onBeforeEnter:O,onEnter:T,onEnterCancelled:C,onLeave:P,onLeaveCancelled:U,onBeforeAppear:X=O,onAppear:q=T,onAppearCancelled:K=C}=t,N=(D,ne,be,Te)=>{D._enterCancelled=Te,Lt(D,ne?c:l),Lt(D,ne?u:i),be&&be()},W=(D,ne)=>{D._isLeaving=!1,Lt(D,f),Lt(D,m),Lt(D,h),ne&&ne()},te=D=>(ne,be)=>{const Te=D?q:T,ie=()=>N(ne,D,be);Wt(Te,[ne,ie]),zr(()=>{Lt(ne,D?a:r),ft(ne,D?c:l),Wr(Te)||qr(ne,s,S,ie)})};return xe(t,{onBeforeEnter(D){Wt(O,[D]),ft(D,r),ft(D,i)},onBeforeAppear(D){Wt(X,[D]),ft(D,a),ft(D,u)},onEnter:te(!1),onAppear:te(!0),onLeave(D,ne){D._isLeaving=!0;const be=()=>W(D,ne);ft(D,f),D._enterCancelled?(ft(D,h),No()):(No(),ft(D,h)),zr(()=>{D._isLeaving&&(Lt(D,f),ft(D,m),Wr(P)||qr(D,s,_,be))}),Wt(P,[D,be])},onEnterCancelled(D){N(D,!1,void 0,!0),Wt(C,[D])},onAppearCancelled(D){N(D,!0,void 0,!0),Wt(K,[D])},onLeaveCancelled(D){W(D),Wt(U,[D])}})}function sf(e){if(e==null)return null;if(de(e))return[fo(e.enter),fo(e.leave)];{const t=fo(e);return[t,t]}}function fo(e){return ac(e)}function ft(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[hn]||(e[hn]=new Set)).add(t)}function Lt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[hn];n&&(n.delete(t),n.size||(e[hn]=void 0))}function zr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let of=0;function qr(e,t,n,s){const o=e._endId=++of,r=()=>{o===e._endId&&s()};if(n!=null)return setTimeout(r,n);const{type:i,timeout:l,propCount:a}=la(e,t);if(!i)return s();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,h),r()},h=m=>{m.target===e&&++c>=a&&f()};setTimeout(()=>{c<a&&f()},l+1),e.addEventListener(u,h)}function la(e,t){const n=window.getComputedStyle(e),s=y=>(n[y]||"").split(", "),o=s(`${Pt}Delay`),r=s(`${Pt}Duration`),i=Jr(o,r),l=s(`${Sn}Delay`),a=s(`${Sn}Duration`),u=Jr(l,a);let c=null,f=0,h=0;t===Pt?i>0&&(c=Pt,f=i,h=r.length):t===Sn?u>0&&(c=Sn,f=u,h=a.length):(f=Math.max(i,u),c=f>0?i>u?Pt:Sn:null,h=c?c===Pt?r.length:a.length:0);const m=c===Pt&&/\b(transform|all)(,|$)/.test(s(`${Pt}Property`).toString());return{type:c,timeout:f,propCount:h,hasTransform:m}}function Jr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Xr(n)+Xr(e[s])))}function Xr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function No(){return document.body.offsetHeight}function rf(e,t,n){const s=e[hn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Yr=Symbol("_vod"),lf=Symbol("_vsh"),af=Symbol(""),cf=/(^|;)\s*display\s*:/;function uf(e,t,n){const s=e.style,o=Ce(n);let r=!1;if(n&&!o){if(t)if(Ce(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&us(s,l,"")}else for(const i in t)n[i]==null&&us(s,i,"");for(const i in n)i==="display"&&(r=!0),us(s,i,n[i])}else if(o){if(t!==n){const i=s[af];i&&(n+=";"+i),s.cssText=n,r=cf.test(n)}}else t&&e.removeAttribute("style");Yr in e&&(e[Yr]=r?s.display:"",e[lf]&&(s.display="none"))}const Qr=/\s*!important$/;function us(e,t,n){if($(n))n.forEach(s=>us(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=ff(e,t);Qr.test(n)?e.setProperty($t(s),n.replace(Qr,""),"important"):e[s]=n}}const Zr=["Webkit","Moz","ms"],ho={};function ff(e,t){const n=ho[t];if(n)return n;let s=Ze(t);if(s!=="filter"&&s in e)return ho[t]=s;s=Ns(s);for(let o=0;o<Zr.length;o++){const r=Zr[o]+s;if(r in e)return ho[t]=r}return t}const ei="http://www.w3.org/1999/xlink";function ti(e,t,n,s,o,r=pc(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ei,t.slice(6,t.length)):e.setAttributeNS(ei,t,n):n==null||r&&!Ji(n)?e.removeAttribute(t):e.setAttribute(t,r?"":yt(n)?String(n):n)}function ni(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?sa(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ji(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function Jt(e,t,n,s){e.addEventListener(t,n,s)}function df(e,t,n,s){e.removeEventListener(t,n,s)}const si=Symbol("_vei");function hf(e,t,n,s,o=null){const r=e[si]||(e[si]={}),i=r[t];if(s&&i)i.value=s;else{const[l,a]=pf(t);if(s){const u=r[t]=yf(s,o);Jt(e,l,u,a)}else i&&(df(e,l,i,a),r[t]=void 0)}}const oi=/(?:Once|Passive|Capture)$/;function pf(e){let t;if(oi.test(e)){t={};let s;for(;s=e.match(oi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):$t(e.slice(2)),t]}let po=0;const mf=Promise.resolve(),gf=()=>po||(mf.then(()=>po=0),po=Date.now());function yf(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;rt(bf(s,n.value),t,5,[s])};return n.value=e,n.attached=gf(),n}function bf(e,t){if($(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const ri=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,vf=(e,t,n,s,o,r)=>{const i=o==="svg";t==="class"?rf(e,s,i):t==="style"?uf(e,n,s):Is(t)?qo(t)||hf(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Sf(e,t,s,i))?(ni(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ti(e,t,s,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ce(s))?ni(e,Ze(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ti(e,t,s,i))};function Sf(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ri(t)&&Q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return ri(t)&&Ce(n)?!1:t in e}const aa=new WeakMap,ca=new WeakMap,Cs=Symbol("_moveCb"),ii=Symbol("_enterCb"),wf=e=>(delete e.props.mode,e),_f=wf({name:"TransitionGroup",props:xe({},ra,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Gs(),s=Rl();let o,r;return Nl(()=>{if(!o.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Tf(o[0].el,n.vnode.el,i)){o=[];return}o.forEach(Cf),o.forEach(Ef);const l=o.filter(Rf);No(),l.forEach(a=>{const u=a.el,c=u.style;ft(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[Cs]=h=>{h&&h.target!==u||(!h||/transform$/.test(h.propertyName))&&(u.removeEventListener("transitionend",f),u[Cs]=null,Lt(u,i))};u.addEventListener("transitionend",f)}),o=[]}),()=>{const i=oe(e),l=ia(i);let a=i.tag||ve;if(o=[],r)for(let u=0;u<r.length;u++){const c=r[u];c.el&&c.el instanceof Element&&(o.push(c),Zt(c,Un(c,l,s,n)),aa.set(c,c.el.getBoundingClientRect()))}r=t.default?ir(t.default()):[];for(let u=0;u<r.length;u++){const c=r[u];c.key!=null&&Zt(c,Un(c,l,s,n))}return Se(a,null,r)}}}),xf=_f;function Cf(e){const t=e.el;t[Cs]&&t[Cs](),t[ii]&&t[ii]()}function Ef(e){ca.set(e,e.el.getBoundingClientRect())}function Rf(e){const t=aa.get(e),n=ca.get(e),s=t.left-n.left,o=t.top-n.top;if(s||o){const r=e.el.style;return r.transform=r.webkitTransform=`translate(${s}px,${o}px)`,r.transitionDuration="0s",e}}function Tf(e,t,n){const s=e.cloneNode(),o=e[hn];o&&o.forEach(l=>{l.split(/\s+/).forEach(a=>a&&s.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&s.classList.add(l)),s.style.display="none";const r=t.nodeType===1?t:t.parentNode;r.appendChild(s);const{hasTransform:i}=la(s);return r.removeChild(s),i}const Es=e=>{const t=e.props["onUpdate:modelValue"]||!1;return $(t)?n=>os(t,n):t};function Af(e){e.target.composing=!0}function li(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dn=Symbol("_assign"),Rs={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[dn]=Es(o);const r=s||o.props&&o.props.type==="number";Jt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),r&&(l=Co(l)),e[dn](l)}),n&&Jt(e,"change",()=>{e.value=e.value.trim()}),t||(Jt(e,"compositionstart",Af),Jt(e,"compositionend",li),Jt(e,"change",li))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[dn]=Es(i),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?Co(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===a)||(e.value=a))}},Pf={deep:!0,created(e,t,n){e[dn]=Es(n),Jt(e,"change",()=>{const s=e._modelValue,o=Of(e),r=e.checked,i=e[dn];if($(s)){const l=Xi(s,o),a=l!==-1;if(r&&!a)i(s.concat(o));else if(!r&&a){const u=[...s];u.splice(l,1),i(u)}}else if(Ls(s)){const l=new Set(s);r?l.add(o):l.delete(o),i(l)}else i(ua(e,r))})},mounted:ai,beforeUpdate(e,t,n){e[dn]=Es(n),ai(e,t,n)}};function ai(e,{value:t,oldValue:n},s){e._modelValue=t;let o;if($(t))o=Xi(t,s.props.value)>-1;else if(Ls(t))o=t.has(s.props.value);else{if(t===n)return;o=Ms(t,ua(e,!0))}e.checked!==o&&(e.checked=o)}function Of(e){return"_value"in e?e._value:e.value}function ua(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const If=["ctrl","shift","alt","meta"],Lf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>If.some(n=>e[`${n}Key`]&&!t.includes(n))},In=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<t.length;i++){const l=Lf[t[i]];if(l&&l(o,t))return}return e(o,...r)})},kf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Do=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=o=>{if(!("key"in o))return;const r=$t(o.key);if(t.some(i=>i===r||kf[i]===r))return e(o)})},Nf=xe({patchProp:vf},tf);let ci;function Df(){return ci||(ci=Cu(Nf))}const Mf=(...e)=>{const t=Df().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=Hf(s);if(!o)return;const r=t._component;!Q(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,Ff(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function Ff(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Hf(e){return Ce(e)?document.querySelector(e):e}var Uf=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let fa;const Ws=e=>fa=e,da=Symbol();function Mo(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Ln;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Ln||(Ln={}));function Bf(){const e=el(!0),t=e.run(()=>ge({}));let n=[],s=[];const o=or({install(r){Ws(o),o._a=r,r.provide(da,o),r.config.globalProperties.$pinia=o,s.forEach(i=>n.push(i)),s=[]},use(r){return!this._a&&!Uf?s.push(r):n.push(r),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}const ha=()=>{};function ui(e,t,n,s=ha){e.push(t);const o=()=>{const r=e.indexOf(t);r>-1&&(e.splice(r,1),s())};return!n&&tl()&&gc(o),o}function on(e,...t){e.slice().forEach(n=>{n(...t)})}const jf=e=>e(),fi=Symbol(),mo=Symbol();function Fo(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],o=e[n];Mo(o)&&Mo(s)&&e.hasOwnProperty(n)&&!_e(s)&&!Bt(s)?e[n]=Fo(o,s):e[n]=s}return e}const Vf=Symbol();function $f(e){return!Mo(e)||!e.hasOwnProperty(Vf)}const{assign:kt}=Object;function Gf(e){return!!(_e(e)&&e.effect)}function Kf(e,t,n,s){const{state:o,actions:r,getters:i}=t,l=n.state.value[e];let a;function u(){l||(n.state.value[e]=o?o():{});const c=Uc(n.state.value[e]);return kt(c,r,Object.keys(i||{}).reduce((f,h)=>(f[h]=or(we(()=>{Ws(n);const m=n._s.get(e);return i[h].call(m,m)})),f),{}))}return a=pa(e,u,t,n,s,!0),a}function pa(e,t,n={},s,o,r){let i;const l=kt({actions:{}},n),a={deep:!0};let u,c,f=[],h=[],m;const y=s.state.value[e];!r&&!y&&(s.state.value[e]={}),ge({});let S;function _(K){let N;u=c=!1,typeof K=="function"?(K(s.state.value[e]),N={type:Ln.patchFunction,storeId:e,events:m}):(Fo(s.state.value[e],K),N={type:Ln.patchObject,payload:K,storeId:e,events:m});const W=S=Symbol();Ct().then(()=>{S===W&&(u=!0)}),c=!0,on(f,N,s.state.value[e])}const O=r?function(){const{state:N}=n,W=N?N():{};this.$patch(te=>{kt(te,W)})}:ha;function T(){i.stop(),f=[],h=[],s._s.delete(e)}const C=(K,N="")=>{if(fi in K)return K[mo]=N,K;const W=function(){Ws(s);const te=Array.from(arguments),D=[],ne=[];function be(Z){D.push(Z)}function Te(Z){ne.push(Z)}on(h,{args:te,name:W[mo],store:U,after:be,onError:Te});let ie;try{ie=K.apply(this&&this.$id===e?this:U,te)}catch(Z){throw on(ne,Z),Z}return ie instanceof Promise?ie.then(Z=>(on(D,Z),Z)).catch(Z=>(on(ne,Z),Promise.reject(Z))):(on(D,ie),ie)};return W[fi]=!0,W[mo]=N,W},P={_p:s,$id:e,$onAction:ui.bind(null,h),$patch:_,$reset:O,$subscribe(K,N={}){const W=ui(f,K,N.detached,()=>te()),te=i.run(()=>pt(()=>s.state.value[e],D=>{(N.flush==="sync"?c:u)&&K({storeId:e,type:Ln.direct,events:m},D)},kt({},a,N)));return W},$dispose:T},U=zn(P);s._s.set(e,U);const q=(s._a&&s._a.runWithContext||jf)(()=>s._e.run(()=>(i=el()).run(()=>t({action:C}))));for(const K in q){const N=q[K];if(_e(N)&&!Gf(N)||Bt(N))r||(y&&$f(N)&&(_e(N)?N.value=y[K]:Fo(N,y[K])),s.state.value[e][K]=N);else if(typeof N=="function"){const W=C(N,K);q[K]=W,l.actions[K]=N}}return kt(U,q),kt(oe(U),q),Object.defineProperty(U,"$state",{get:()=>s.state.value[e],set:K=>{_(N=>{kt(N,K)})}}),s._p.forEach(K=>{kt(U,i.run(()=>K({store:U,app:s._a,pinia:s,options:l})))}),y&&r&&n.hydrate&&n.hydrate(U.$state,y),u=!0,c=!0,U}/*! #__NO_SIDE_EFFECTS__ */function ma(e,t,n){let s,o;const r=typeof t=="function";typeof e=="string"?(s=e,o=r?n:t):(o=e,s=e.id);function i(l,a){const u=yu();return l=l||(u?Qe(da,null):null),l&&Ws(l),l=fa,l._s.has(s)||(r?pa(s,t,o,l):Kf(s,o,l)),l._s.get(s)}return i.$id=s,i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ln=typeof document<"u";function ga(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Wf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ga(e.default)}const ae=Object.assign;function go(e,t){const n={};for(const s in t){const o=t[s];n[s]=it(o)?o.map(e):e(o)}return n}const kn=()=>{},it=Array.isArray,ya=/#/g,zf=/&/g,qf=/\//g,Jf=/=/g,Xf=/\?/g,ba=/\+/g,Yf=/%5B/g,Qf=/%5D/g,va=/%5E/g,Zf=/%60/g,Sa=/%7B/g,ed=/%7C/g,wa=/%7D/g,td=/%20/g;function hr(e){return encodeURI(""+e).replace(ed,"|").replace(Yf,"[").replace(Qf,"]")}function nd(e){return hr(e).replace(Sa,"{").replace(wa,"}").replace(va,"^")}function Ho(e){return hr(e).replace(ba,"%2B").replace(td,"+").replace(ya,"%23").replace(zf,"%26").replace(Zf,"`").replace(Sa,"{").replace(wa,"}").replace(va,"^")}function sd(e){return Ho(e).replace(Jf,"%3D")}function od(e){return hr(e).replace(ya,"%23").replace(Xf,"%3F")}function rd(e){return e==null?"":od(e).replace(qf,"%2F")}function $n(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const id=/\/$/,ld=e=>e.replace(id,"");function yo(e,t,n="/"){let s,o={},r="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),r=t.slice(a+1,l>-1?l:t.length),o=e(r)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=fd(s??t,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:$n(i)}}function ad(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function di(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function cd(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&pn(t.matched[s],n.matched[o])&&_a(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function pn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function _a(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ud(e[n],t[n]))return!1;return!0}function ud(e,t){return it(e)?hi(e,t):it(t)?hi(t,e):e===t}function hi(e,t){return it(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function fd(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(i).join("/")}const Ot={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Gn;(function(e){e.pop="pop",e.push="push"})(Gn||(Gn={}));var Nn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Nn||(Nn={}));function dd(e){if(!e)if(ln){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ld(e)}const hd=/^[^#]+#/;function pd(e,t){return e.replace(hd,"#")+t}function md(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const zs=()=>({left:window.scrollX,top:window.scrollY});function gd(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=md(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function pi(e,t){return(history.state?history.state.position-t:-1)+e}const Uo=new Map;function yd(e,t){Uo.set(e,t)}function bd(e){const t=Uo.get(e);return Uo.delete(e),t}let vd=()=>location.protocol+"//"+location.host;function xa(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let l=o.includes(e.slice(r))?e.slice(r).length:1,a=o.slice(l);return a[0]!=="/"&&(a="/"+a),di(a,"")}return di(n,e)+s+o}function Sd(e,t,n,s){let o=[],r=[],i=null;const l=({state:h})=>{const m=xa(e,location),y=n.value,S=t.value;let _=0;if(h){if(n.value=m,t.value=h,i&&i===y){i=null;return}_=S?h.position-S.position:0}else s(m);o.forEach(O=>{O(n.value,y,{delta:_,type:Gn.pop,direction:_?_>0?Nn.forward:Nn.back:Nn.unknown})})};function a(){i=n.value}function u(h){o.push(h);const m=()=>{const y=o.indexOf(h);y>-1&&o.splice(y,1)};return r.push(m),m}function c(){const{history:h}=window;h.state&&h.replaceState(ae({},h.state,{scroll:zs()}),"")}function f(){for(const h of r)h();r=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function mi(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?zs():null}}function wd(e){const{history:t,location:n}=window,s={value:xa(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(a,u,c){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:vd()+e+a;try{t[c?"replaceState":"pushState"](u,"",h),o.value=u}catch(m){console.error(m),n[c?"replace":"assign"](h)}}function i(a,u){const c=ae({},t.state,mi(o.value.back,a,o.value.forward,!0),u,{position:o.value.position});r(a,c,!0),s.value=a}function l(a,u){const c=ae({},o.value,t.state,{forward:a,scroll:zs()});r(c.current,c,!0);const f=ae({},mi(s.value,a,null),{position:c.position+1},u);r(a,f,!1),s.value=a}return{location:s,state:o,push:l,replace:i}}function _d(e){e=dd(e);const t=wd(e),n=Sd(e,t.state,t.location,t.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=ae({location:"",base:e,go:s,createHref:pd.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function xd(e){return typeof e=="string"||e&&typeof e=="object"}function Ca(e){return typeof e=="string"||typeof e=="symbol"}const Ea=Symbol("");var gi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(gi||(gi={}));function mn(e,t){return ae(new Error,{type:e,[Ea]:!0},t)}function vt(e,t){return e instanceof Error&&Ea in e&&(t==null||!!(e.type&t))}const yi="[^/]+?",Cd={sensitive:!1,strict:!1,start:!0,end:!0},Ed=/[.+*?^${}()[\]/\\]/g;function Rd(e,t){const n=ae({},Cd,t),s=[];let o=n.start?"^":"";const r=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const h=u[f];let m=40+(n.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(Ed,"\\$&"),m+=40;else if(h.type===1){const{value:y,repeatable:S,optional:_,regexp:O}=h;r.push({name:y,repeatable:S,optional:_});const T=O||yi;if(T!==yi){m+=10;try{new RegExp(`(${T})`)}catch(P){throw new Error(`Invalid custom RegExp for param "${y}" (${T}): `+P.message)}}let C=S?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;f||(C=_&&u.length<2?`(?:/${C})`:"/"+C),_&&(C+="?"),o+=C,m+=20,_&&(m+=-8),S&&(m+=-20),T===".*"&&(m+=-50)}c.push(m)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let h=1;h<c.length;h++){const m=c[h]||"",y=r[h-1];f[y.name]=m&&y.repeatable?m.split("/"):m}return f}function a(u){let c="",f=!1;for(const h of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const m of h)if(m.type===0)c+=m.value;else if(m.type===1){const{value:y,repeatable:S,optional:_}=m,O=y in u?u[y]:"";if(it(O)&&!S)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const T=it(O)?O.join("/"):O;if(!T)if(_)h.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);c+=T}}return c||"/"}return{re:i,score:s,keys:r,parse:l,stringify:a}}function Td(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ra(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=Td(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(bi(s))return 1;if(bi(o))return-1}return o.length-s.length}function bi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ad={type:0,value:""},Pd=/[a-zA-Z0-9_]/;function Od(e){if(!e)return[[]];if(e==="/")return[[Ad]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let l=0,a,u="",c="";function f(){u&&(n===0?r.push({type:0,value:u}):n===1||n===2||n===3?(r.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:a==="("?n=2:Pd.test(a)?h():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),o}function Id(e,t,n){const s=Rd(Od(e.path),n),o=ae(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Ld(e,t){const n=[],s=new Map;t=_i({strict:!1,end:!0,sensitive:!1},t);function o(f){return s.get(f)}function r(f,h,m){const y=!m,S=Si(f);S.aliasOf=m&&m.record;const _=_i(t,f),O=[S];if("alias"in f){const P=typeof f.alias=="string"?[f.alias]:f.alias;for(const U of P)O.push(Si(ae({},S,{components:m?m.record.components:S.components,path:U,aliasOf:m?m.record:S})))}let T,C;for(const P of O){const{path:U}=P;if(h&&U[0]!=="/"){const X=h.record.path,q=X[X.length-1]==="/"?"":"/";P.path=h.record.path+(U&&q+U)}if(T=Id(P,h,_),m?m.alias.push(T):(C=C||T,C!==T&&C.alias.push(T),y&&f.name&&!wi(T)&&i(f.name)),Ta(T)&&a(T),S.children){const X=S.children;for(let q=0;q<X.length;q++)r(X[q],T,m&&m.children[q])}m=m||T}return C?()=>{i(C)}:kn}function i(f){if(Ca(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const h=Dd(f,n);n.splice(h,0,f),f.record.name&&!wi(f)&&s.set(f.record.name,f)}function u(f,h){let m,y={},S,_;if("name"in f&&f.name){if(m=s.get(f.name),!m)throw mn(1,{location:f});_=m.record.name,y=ae(vi(h.params,m.keys.filter(C=>!C.optional).concat(m.parent?m.parent.keys.filter(C=>C.optional):[]).map(C=>C.name)),f.params&&vi(f.params,m.keys.map(C=>C.name))),S=m.stringify(y)}else if(f.path!=null)S=f.path,m=n.find(C=>C.re.test(S)),m&&(y=m.parse(S),_=m.record.name);else{if(m=h.name?s.get(h.name):n.find(C=>C.re.test(h.path)),!m)throw mn(1,{location:f,currentLocation:h});_=m.record.name,y=ae({},h.params,f.params),S=m.stringify(y)}const O=[];let T=m;for(;T;)O.unshift(T.record),T=T.parent;return{name:_,path:S,params:y,matched:O,meta:Nd(O)}}e.forEach(f=>r(f));function c(){n.length=0,s.clear()}return{addRoute:r,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:o}}function vi(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Si(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:kd(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function kd(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function wi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Nd(e){return e.reduce((t,n)=>ae(t,n.meta),{})}function _i(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Dd(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;Ra(e,t[r])<0?s=r:n=r+1}const o=Md(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function Md(e){let t=e;for(;t=t.parent;)if(Ta(t)&&Ra(e,t)===0)return t}function Ta({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Fd(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace(ba," "),i=r.indexOf("="),l=$n(i<0?r:r.slice(0,i)),a=i<0?null:$n(r.slice(i+1));if(l in t){let u=t[l];it(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function xi(e){let t="";for(let n in e){const s=e[n];if(n=sd(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(it(s)?s.map(r=>r&&Ho(r)):[s&&Ho(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function Hd(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=it(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const Ud=Symbol(""),Ci=Symbol(""),qs=Symbol(""),pr=Symbol(""),Bo=Symbol("");function wn(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ft(e,t,n,s,o,r=i=>i()){const i=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((l,a)=>{const u=h=>{h===!1?a(mn(4,{from:n,to:t})):h instanceof Error?a(h):xd(h)?a(mn(2,{from:t,to:h})):(i&&s.enterCallbacks[o]===i&&typeof h=="function"&&i.push(h),l())},c=r(()=>e.call(s&&s.instances[o],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(h=>a(h))})}function bo(e,t,n,s,o=r=>r()){const r=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(ga(a)){const c=(a.__vccOpts||a)[t];c&&r.push(Ft(c,n,s,i,l,o))}else{let u=a();r.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=Wf(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const m=(f.__vccOpts||f)[t];return m&&Ft(m,n,s,i,l,o)()}))}}return r}function Ei(e){const t=Qe(qs),n=Qe(pr),s=we(()=>{const a=un(e.to);return t.resolve(a)}),o=we(()=>{const{matched:a}=s.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const h=f.findIndex(pn.bind(null,c));if(h>-1)return h;const m=Ri(a[u-2]);return u>1&&Ri(c)===m&&f[f.length-1].path!==m?f.findIndex(pn.bind(null,a[u-2])):h}),r=we(()=>o.value>-1&&Gd(n.params,s.value.params)),i=we(()=>o.value>-1&&o.value===n.matched.length-1&&_a(n.params,s.value.params));function l(a={}){if($d(a)){const u=t[un(e.replace)?"replace":"push"](un(e.to)).catch(kn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:we(()=>s.value.href),isActive:r,isExactActive:i,navigate:l}}function Bd(e){return e.length===1?e[0]:e}const jd=Il({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ei,setup(e,{slots:t}){const n=zn(Ei(e)),{options:s}=Qe(qs),o=we(()=>({[Ti(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Ti(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&Bd(t.default(n));return e.custom?r:dr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),Vd=jd;function $d(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Gd(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!it(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function Ri(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ti=(e,t,n)=>e??t??n,Kd=Il({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Qe(Bo),o=we(()=>e.route||s.value),r=Qe(Ci,0),i=we(()=>{let u=un(r);const{matched:c}=o.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=we(()=>o.value.matched[i.value]);is(Ci,we(()=>i.value+1)),is(Ud,l),is(Bo,o);const a=ge();return pt(()=>[a.value,l.value,e.name],([u,c,f],[h,m,y])=>{c&&(c.instances[f]=u,m&&m!==c&&u&&u===h&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),u&&c&&(!m||!pn(c,m)||!h)&&(c.enterCallbacks[f]||[]).forEach(S=>S(u))},{flush:"post"}),()=>{const u=o.value,c=e.name,f=l.value,h=f&&f.components[c];if(!h)return Ai(n.default,{Component:h,route:u});const m=f.props[c],y=m?m===!0?u.params:typeof m=="function"?m(u):m:null,_=dr(h,ae({},y,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return Ai(n.default,{Component:_,route:u})||_}}});function Ai(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Wd=Kd;function zd(e){const t=Ld(e.routes,e),n=e.parseQuery||Fd,s=e.stringifyQuery||xi,o=e.history,r=wn(),i=wn(),l=wn(),a=Mc(Ot);let u=Ot;ln&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=go.bind(null,x=>""+x),f=go.bind(null,rd),h=go.bind(null,$n);function m(x,B){let F,j;return Ca(x)?(F=t.getRecordMatcher(x),j=B):j=x,t.addRoute(j,F)}function y(x){const B=t.getRecordMatcher(x);B&&t.removeRoute(B)}function S(){return t.getRoutes().map(x=>x.record)}function _(x){return!!t.getRecordMatcher(x)}function O(x,B){if(B=ae({},B||a.value),typeof x=="string"){const p=yo(n,x,B.path),b=t.resolve({path:p.path},B),E=o.createHref(p.fullPath);return ae(p,b,{params:h(b.params),hash:$n(p.hash),redirectedFrom:void 0,href:E})}let F;if(x.path!=null)F=ae({},x,{path:yo(n,x.path,B.path).path});else{const p=ae({},x.params);for(const b in p)p[b]==null&&delete p[b];F=ae({},x,{params:f(p)}),B.params=f(B.params)}const j=t.resolve(F,B),le=x.hash||"";j.params=c(h(j.params));const me=ad(s,ae({},x,{hash:nd(le),path:j.path})),d=o.createHref(me);return ae({fullPath:me,hash:le,query:s===xi?Hd(x.query):x.query||{}},j,{redirectedFrom:void 0,href:d})}function T(x){return typeof x=="string"?yo(n,x,a.value.path):ae({},x)}function C(x,B){if(u!==x)return mn(8,{from:B,to:x})}function P(x){return q(x)}function U(x){return P(ae(T(x),{replace:!0}))}function X(x){const B=x.matched[x.matched.length-1];if(B&&B.redirect){const{redirect:F}=B;let j=typeof F=="function"?F(x):F;return typeof j=="string"&&(j=j.includes("?")||j.includes("#")?j=T(j):{path:j},j.params={}),ae({query:x.query,hash:x.hash,params:j.path!=null?{}:x.params},j)}}function q(x,B){const F=u=O(x),j=a.value,le=x.state,me=x.force,d=x.replace===!0,p=X(F);if(p)return q(ae(T(p),{state:typeof p=="object"?ae({},le,p.state):le,force:me,replace:d}),B||F);const b=F;b.redirectedFrom=B;let E;return!me&&cd(s,j,F)&&(E=mn(16,{to:b,from:j}),$e(j,j,!0,!1)),(E?Promise.resolve(E):W(b,j)).catch(w=>vt(w)?vt(w,2)?w:Pe(w):I(w,b,j)).then(w=>{if(w){if(vt(w,2))return q(ae({replace:d},T(w.to),{state:typeof w.to=="object"?ae({},le,w.to.state):le,force:me}),B||b)}else w=D(b,j,!0,d,le);return te(b,j,w),w})}function K(x,B){const F=C(x,B);return F?Promise.reject(F):Promise.resolve()}function N(x){const B=nn.values().next().value;return B&&typeof B.runWithContext=="function"?B.runWithContext(x):x()}function W(x,B){let F;const[j,le,me]=qd(x,B);F=bo(j.reverse(),"beforeRouteLeave",x,B);for(const p of j)p.leaveGuards.forEach(b=>{F.push(Ft(b,x,B))});const d=K.bind(null,x,B);return F.push(d),Je(F).then(()=>{F=[];for(const p of r.list())F.push(Ft(p,x,B));return F.push(d),Je(F)}).then(()=>{F=bo(le,"beforeRouteUpdate",x,B);for(const p of le)p.updateGuards.forEach(b=>{F.push(Ft(b,x,B))});return F.push(d),Je(F)}).then(()=>{F=[];for(const p of me)if(p.beforeEnter)if(it(p.beforeEnter))for(const b of p.beforeEnter)F.push(Ft(b,x,B));else F.push(Ft(p.beforeEnter,x,B));return F.push(d),Je(F)}).then(()=>(x.matched.forEach(p=>p.enterCallbacks={}),F=bo(me,"beforeRouteEnter",x,B,N),F.push(d),Je(F))).then(()=>{F=[];for(const p of i.list())F.push(Ft(p,x,B));return F.push(d),Je(F)}).catch(p=>vt(p,8)?p:Promise.reject(p))}function te(x,B,F){l.list().forEach(j=>N(()=>j(x,B,F)))}function D(x,B,F,j,le){const me=C(x,B);if(me)return me;const d=B===Ot,p=ln?history.state:{};F&&(j||d?o.replace(x.fullPath,ae({scroll:d&&p&&p.scroll},le)):o.push(x.fullPath,le)),a.value=x,$e(x,B,F,d),Pe()}let ne;function be(){ne||(ne=o.listen((x,B,F)=>{if(!Zn.listening)return;const j=O(x),le=X(j);if(le){q(ae(le,{replace:!0,force:!0}),j).catch(kn);return}u=j;const me=a.value;ln&&yd(pi(me.fullPath,F.delta),zs()),W(j,me).catch(d=>vt(d,12)?d:vt(d,2)?(q(ae(T(d.to),{force:!0}),j).then(p=>{vt(p,20)&&!F.delta&&F.type===Gn.pop&&o.go(-1,!1)}).catch(kn),Promise.reject()):(F.delta&&o.go(-F.delta,!1),I(d,j,me))).then(d=>{d=d||D(j,me,!1),d&&(F.delta&&!vt(d,8)?o.go(-F.delta,!1):F.type===Gn.pop&&vt(d,20)&&o.go(-1,!1)),te(j,me,d)}).catch(kn)}))}let Te=wn(),ie=wn(),Z;function I(x,B,F){Pe(x);const j=ie.list();return j.length?j.forEach(le=>le(x,B,F)):console.error(x),Promise.reject(x)}function re(){return Z&&a.value!==Ot?Promise.resolve():new Promise((x,B)=>{Te.add([x,B])})}function Pe(x){return Z||(Z=!x,be(),Te.list().forEach(([B,F])=>x?F(x):B()),Te.reset()),x}function $e(x,B,F,j){const{scrollBehavior:le}=e;if(!ln||!le)return Promise.resolve();const me=!F&&bd(pi(x.fullPath,0))||(j||!F)&&history.state&&history.state.scroll||null;return Ct().then(()=>le(x,B,me)).then(d=>d&&gd(d)).catch(d=>I(d,x,B))}const Ee=x=>o.go(x);let Ue;const nn=new Set,Zn={currentRoute:a,listening:!0,addRoute:m,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:S,resolve:O,options:e,push:P,replace:U,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:r.add,beforeResolve:i.add,afterEach:l.add,onError:ie.add,isReady:re,install(x){const B=this;x.component("RouterLink",Vd),x.component("RouterView",Wd),x.config.globalProperties.$router=B,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>un(a)}),ln&&!Ue&&a.value===Ot&&(Ue=!0,P(o.location).catch(le=>{}));const F={};for(const le in Ot)Object.defineProperty(F,le,{get:()=>a.value[le],enumerable:!0});x.provide(qs,B),x.provide(pr,gl(F)),x.provide(Bo,a);const j=x.unmount;nn.add(x),x.unmount=function(){nn.delete(x),nn.size<1&&(u=Ot,ne&&ne(),ne=null,a.value=Ot,Ue=!1,Z=!1),j()}}};function Je(x){return x.reduce((B,F)=>B.then(()=>N(F)),Promise.resolve())}return Zn}function qd(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let i=0;i<r;i++){const l=t.matched[i];l&&(e.matched.find(u=>pn(u,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>pn(u,a))||o.push(a))}return[n,s,o]}function Aa(){return Qe(qs)}function Jd(e){return Qe(pr)}const tn=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},Xd={name:"App"},Yd={id:"app"};function Qd(e,t,n,s,o,r){const i=Ss("router-view");return z(),J("div",Yd,[Se(i)])}const Zd=tn(Xd,[["render",Qd]]);function Pa(e,t){return function(){return e.apply(t,arguments)}}const{toString:eh}=Object.prototype,{getPrototypeOf:mr}=Object,{iterator:Js,toStringTag:Oa}=Symbol,Xs=(e=>t=>{const n=eh.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),lt=e=>(e=e.toLowerCase(),t=>Xs(t)===e),Ys=e=>t=>typeof t===e,{isArray:gn}=Array,Kn=Ys("undefined");function Xn(e){return e!==null&&!Kn(e)&&e.constructor!==null&&!Kn(e.constructor)&&Ve(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ia=lt("ArrayBuffer");function th(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ia(e.buffer),t}const nh=Ys("string"),Ve=Ys("function"),La=Ys("number"),Yn=e=>e!==null&&typeof e=="object",sh=e=>e===!0||e===!1,fs=e=>{if(Xs(e)!=="object")return!1;const t=mr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Oa in e)&&!(Js in e)},oh=e=>{if(!Yn(e)||Xn(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},rh=lt("Date"),ih=lt("File"),lh=lt("Blob"),ah=lt("FileList"),ch=e=>Yn(e)&&Ve(e.pipe),uh=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ve(e.append)&&((t=Xs(e))==="formdata"||t==="object"&&Ve(e.toString)&&e.toString()==="[object FormData]"))},fh=lt("URLSearchParams"),[dh,hh,ph,mh]=["ReadableStream","Request","Response","Headers"].map(lt),gh=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Qn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,o;if(typeof e!="object"&&(e=[e]),gn(e))for(s=0,o=e.length;s<o;s++)t.call(null,e[s],s,e);else{if(Xn(e))return;const r=n?Object.getOwnPropertyNames(e):Object.keys(e),i=r.length;let l;for(s=0;s<i;s++)l=r[s],t.call(null,e[l],l,e)}}function ka(e,t){if(Xn(e))return null;t=t.toLowerCase();const n=Object.keys(e);let s=n.length,o;for(;s-- >0;)if(o=n[s],t===o.toLowerCase())return o;return null}const Xt=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Na=e=>!Kn(e)&&e!==Xt;function jo(){const{caseless:e}=Na(this)&&this||{},t={},n=(s,o)=>{const r=e&&ka(t,o)||o;fs(t[r])&&fs(s)?t[r]=jo(t[r],s):fs(s)?t[r]=jo({},s):gn(s)?t[r]=s.slice():t[r]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&Qn(arguments[s],n);return t}const yh=(e,t,n,{allOwnKeys:s}={})=>(Qn(t,(o,r)=>{n&&Ve(o)?e[r]=Pa(o,n):e[r]=o},{allOwnKeys:s}),e),bh=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),vh=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Sh=(e,t,n,s)=>{let o,r,i;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),r=o.length;r-- >0;)i=o[r],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&mr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},wh=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},_h=e=>{if(!e)return null;if(gn(e))return e;let t=e.length;if(!La(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},xh=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&mr(Uint8Array)),Ch=(e,t)=>{const s=(e&&e[Js]).call(e);let o;for(;(o=s.next())&&!o.done;){const r=o.value;t.call(e,r[0],r[1])}},Eh=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Rh=lt("HTMLFormElement"),Th=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,o){return s.toUpperCase()+o}),Pi=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Ah=lt("RegExp"),Da=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Qn(n,(o,r)=>{let i;(i=t(o,r,e))!==!1&&(s[r]=i||o)}),Object.defineProperties(e,s)},Ph=e=>{Da(e,(t,n)=>{if(Ve(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Ve(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Oh=(e,t)=>{const n={},s=o=>{o.forEach(r=>{n[r]=!0})};return gn(e)?s(e):s(String(e).split(t)),n},Ih=()=>{},Lh=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function kh(e){return!!(e&&Ve(e.append)&&e[Oa]==="FormData"&&e[Js])}const Nh=e=>{const t=new Array(10),n=(s,o)=>{if(Yn(s)){if(t.indexOf(s)>=0)return;if(Xn(s))return s;if(!("toJSON"in s)){t[o]=s;const r=gn(s)?[]:{};return Qn(s,(i,l)=>{const a=n(i,o+1);!Kn(a)&&(r[l]=a)}),t[o]=void 0,r}}return s};return n(e,0)},Dh=lt("AsyncFunction"),Mh=e=>e&&(Yn(e)||Ve(e))&&Ve(e.then)&&Ve(e.catch),Ma=((e,t)=>e?setImmediate:t?((n,s)=>(Xt.addEventListener("message",({source:o,data:r})=>{o===Xt&&r===n&&s.length&&s.shift()()},!1),o=>{s.push(o),Xt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ve(Xt.postMessage)),Fh=typeof queueMicrotask<"u"?queueMicrotask.bind(Xt):typeof process<"u"&&process.nextTick||Ma,Hh=e=>e!=null&&Ve(e[Js]),v={isArray:gn,isArrayBuffer:Ia,isBuffer:Xn,isFormData:uh,isArrayBufferView:th,isString:nh,isNumber:La,isBoolean:sh,isObject:Yn,isPlainObject:fs,isEmptyObject:oh,isReadableStream:dh,isRequest:hh,isResponse:ph,isHeaders:mh,isUndefined:Kn,isDate:rh,isFile:ih,isBlob:lh,isRegExp:Ah,isFunction:Ve,isStream:ch,isURLSearchParams:fh,isTypedArray:xh,isFileList:ah,forEach:Qn,merge:jo,extend:yh,trim:gh,stripBOM:bh,inherits:vh,toFlatObject:Sh,kindOf:Xs,kindOfTest:lt,endsWith:wh,toArray:_h,forEachEntry:Ch,matchAll:Eh,isHTMLForm:Rh,hasOwnProperty:Pi,hasOwnProp:Pi,reduceDescriptors:Da,freezeMethods:Ph,toObjectSet:Oh,toCamelCase:Th,noop:Ih,toFiniteNumber:Lh,findKey:ka,global:Xt,isContextDefined:Na,isSpecCompliantForm:kh,toJSONObject:Nh,isAsyncFn:Dh,isThenable:Mh,setImmediate:Ma,asap:Fh,isIterable:Hh};function ee(e,t,n,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}v.inherits(ee,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:v.toJSONObject(this.config),code:this.code,status:this.status}}});const Fa=ee.prototype,Ha={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ha[e]={value:e}});Object.defineProperties(ee,Ha);Object.defineProperty(Fa,"isAxiosError",{value:!0});ee.from=(e,t,n,s,o,r)=>{const i=Object.create(Fa);return v.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),ee.call(i,e.message,t,n,s,o),i.cause=e,i.name=e.name,r&&Object.assign(i,r),i};const Uh=null;function Vo(e){return v.isPlainObject(e)||v.isArray(e)}function Ua(e){return v.endsWith(e,"[]")?e.slice(0,-2):e}function Oi(e,t,n){return e?e.concat(t).map(function(o,r){return o=Ua(o),!n&&r?"["+o+"]":o}).join(n?".":""):t}function Bh(e){return v.isArray(e)&&!e.some(Vo)}const jh=v.toFlatObject(v,{},null,function(t){return/^is[A-Z]/.test(t)});function Qs(e,t,n){if(!v.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=v.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,_){return!v.isUndefined(_[S])});const s=n.metaTokens,o=n.visitor||c,r=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&v.isSpecCompliantForm(t);if(!v.isFunction(o))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(v.isDate(y))return y.toISOString();if(v.isBoolean(y))return y.toString();if(!a&&v.isBlob(y))throw new ee("Blob is not supported. Use a Buffer instead.");return v.isArrayBuffer(y)||v.isTypedArray(y)?a&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,S,_){let O=y;if(y&&!_&&typeof y=="object"){if(v.endsWith(S,"{}"))S=s?S:S.slice(0,-2),y=JSON.stringify(y);else if(v.isArray(y)&&Bh(y)||(v.isFileList(y)||v.endsWith(S,"[]"))&&(O=v.toArray(y)))return S=Ua(S),O.forEach(function(C,P){!(v.isUndefined(C)||C===null)&&t.append(i===!0?Oi([S],P,r):i===null?S:S+"[]",u(C))}),!1}return Vo(y)?!0:(t.append(Oi(_,S,r),u(y)),!1)}const f=[],h=Object.assign(jh,{defaultVisitor:c,convertValue:u,isVisitable:Vo});function m(y,S){if(!v.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+S.join("."));f.push(y),v.forEach(y,function(O,T){(!(v.isUndefined(O)||O===null)&&o.call(t,O,v.isString(T)?T.trim():T,S,h))===!0&&m(O,S?S.concat(T):[T])}),f.pop()}}if(!v.isObject(e))throw new TypeError("data must be an object");return m(e),t}function Ii(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function gr(e,t){this._pairs=[],e&&Qs(e,this,t)}const Ba=gr.prototype;Ba.append=function(t,n){this._pairs.push([t,n])};Ba.toString=function(t){const n=t?function(s){return t.call(this,s,Ii)}:Ii;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function Vh(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ja(e,t,n){if(!t)return e;const s=n&&n.encode||Vh;v.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let r;if(o?r=o(t,n):r=v.isURLSearchParams(t)?t.toString():new gr(t,n).toString(s),r){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+r}return e}class $h{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){v.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Li=$h,Va={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gh=typeof URLSearchParams<"u"?URLSearchParams:gr,Kh=typeof FormData<"u"?FormData:null,Wh=typeof Blob<"u"?Blob:null,zh={isBrowser:!0,classes:{URLSearchParams:Gh,FormData:Kh,Blob:Wh},protocols:["http","https","file","blob","url","data"]},yr=typeof window<"u"&&typeof document<"u",$o=typeof navigator=="object"&&navigator||void 0,qh=yr&&(!$o||["ReactNative","NativeScript","NS"].indexOf($o.product)<0),Jh=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Xh=yr&&window.location.href||"http://localhost",Yh=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:yr,hasStandardBrowserEnv:qh,hasStandardBrowserWebWorkerEnv:Jh,navigator:$o,origin:Xh},Symbol.toStringTag,{value:"Module"})),Ne={...Yh,...zh};function Qh(e,t){return Qs(e,new Ne.classes.URLSearchParams,{visitor:function(n,s,o,r){return Ne.isNode&&v.isBuffer(n)?(this.append(s,n.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)},...t})}function Zh(e){return v.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ep(e){const t={},n=Object.keys(e);let s;const o=n.length;let r;for(s=0;s<o;s++)r=n[s],t[r]=e[r];return t}function $a(e){function t(n,s,o,r){let i=n[r++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=r>=n.length;return i=!i&&v.isArray(o)?o.length:i,a?(v.hasOwnProp(o,i)?o[i]=[o[i],s]:o[i]=s,!l):((!o[i]||!v.isObject(o[i]))&&(o[i]=[]),t(n,s,o[i],r)&&v.isArray(o[i])&&(o[i]=ep(o[i])),!l)}if(v.isFormData(e)&&v.isFunction(e.entries)){const n={};return v.forEachEntry(e,(s,o)=>{t(Zh(s),o,n,0)}),n}return null}function tp(e,t,n){if(v.isString(e))try{return(t||JSON.parse)(e),v.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const br={transitional:Va,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",o=s.indexOf("application/json")>-1,r=v.isObject(t);if(r&&v.isHTMLForm(t)&&(t=new FormData(t)),v.isFormData(t))return o?JSON.stringify($a(t)):t;if(v.isArrayBuffer(t)||v.isBuffer(t)||v.isStream(t)||v.isFile(t)||v.isBlob(t)||v.isReadableStream(t))return t;if(v.isArrayBufferView(t))return t.buffer;if(v.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(r){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Qh(t,this.formSerializer).toString();if((l=v.isFileList(t))||s.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Qs(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return r||o?(n.setContentType("application/json",!1),tp(t)):t}],transformResponse:[function(t){const n=this.transitional||br.transitional,s=n&&n.forcedJSONParsing,o=this.responseType==="json";if(v.isResponse(t)||v.isReadableStream(t))return t;if(t&&v.isString(t)&&(s&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?ee.from(l,ee.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ne.classes.FormData,Blob:Ne.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};v.forEach(["delete","get","head","post","put","patch"],e=>{br.headers[e]={}});const vr=br,np=v.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),sp=e=>{const t={};let n,s,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),s=i.substring(o+1).trim(),!(!n||t[n]&&np[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},ki=Symbol("internals");function _n(e){return e&&String(e).trim().toLowerCase()}function ds(e){return e===!1||e==null?e:v.isArray(e)?e.map(ds):String(e)}function op(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const rp=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function vo(e,t,n,s,o){if(v.isFunction(s))return s.call(this,t,n);if(o&&(t=n),!!v.isString(t)){if(v.isString(s))return t.indexOf(s)!==-1;if(v.isRegExp(s))return s.test(t)}}function ip(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function lp(e,t){const n=v.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(o,r,i){return this[s].call(this,t,o,r,i)},configurable:!0})})}class Zs{constructor(t){t&&this.set(t)}set(t,n,s){const o=this;function r(l,a,u){const c=_n(a);if(!c)throw new Error("header name must be a non-empty string");const f=v.findKey(o,c);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||a]=ds(l))}const i=(l,a)=>v.forEach(l,(u,c)=>r(u,c,a));if(v.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(v.isString(t)&&(t=t.trim())&&!rp(t))i(sp(t),n);else if(v.isObject(t)&&v.isIterable(t)){let l={},a,u;for(const c of t){if(!v.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?v.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&r(n,t,s);return this}get(t,n){if(t=_n(t),t){const s=v.findKey(this,t);if(s){const o=this[s];if(!n)return o;if(n===!0)return op(o);if(v.isFunction(n))return n.call(this,o,s);if(v.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=_n(t),t){const s=v.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||vo(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let o=!1;function r(i){if(i=_n(i),i){const l=v.findKey(s,i);l&&(!n||vo(s,s[l],l,n))&&(delete s[l],o=!0)}}return v.isArray(t)?t.forEach(r):r(t),o}clear(t){const n=Object.keys(this);let s=n.length,o=!1;for(;s--;){const r=n[s];(!t||vo(this,this[r],r,t,!0))&&(delete this[r],o=!0)}return o}normalize(t){const n=this,s={};return v.forEach(this,(o,r)=>{const i=v.findKey(s,r);if(i){n[i]=ds(o),delete n[r];return}const l=t?ip(r):String(r).trim();l!==r&&delete n[r],n[l]=ds(o),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return v.forEach(this,(s,o)=>{s!=null&&s!==!1&&(n[o]=t&&v.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(o=>s.set(o)),s}static accessor(t){const s=(this[ki]=this[ki]={accessors:{}}).accessors,o=this.prototype;function r(i){const l=_n(i);s[l]||(lp(o,i),s[l]=!0)}return v.isArray(t)?t.forEach(r):r(t),this}}Zs.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);v.reduceDescriptors(Zs.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});v.freezeMethods(Zs);const ot=Zs;function So(e,t){const n=this||vr,s=t||n,o=ot.from(s.headers);let r=s.data;return v.forEach(e,function(l){r=l.call(n,r,o.normalize(),t?t.status:void 0)}),o.normalize(),r}function Ga(e){return!!(e&&e.__CANCEL__)}function yn(e,t,n){ee.call(this,e??"canceled",ee.ERR_CANCELED,t,n),this.name="CanceledError"}v.inherits(yn,ee,{__CANCEL__:!0});function Ka(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new ee("Request failed with status code "+n.status,[ee.ERR_BAD_REQUEST,ee.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function ap(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function cp(e,t){e=e||10;const n=new Array(e),s=new Array(e);let o=0,r=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=s[r];i||(i=u),n[o]=a,s[o]=u;let f=r,h=0;for(;f!==o;)h+=n[f++],f=f%e;if(o=(o+1)%e,o===r&&(r=(r+1)%e),u-i<t)return;const m=c&&u-c;return m?Math.round(h*1e3/m):void 0}}function up(e,t){let n=0,s=1e3/t,o,r;const i=(u,c=Date.now())=>{n=c,o=null,r&&(clearTimeout(r),r=null),e(...u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(o=u,r||(r=setTimeout(()=>{r=null,i(o)},s-f)))},()=>o&&i(o)]}const Ts=(e,t,n=3)=>{let s=0;const o=cp(50,250);return up(r=>{const i=r.loaded,l=r.lengthComputable?r.total:void 0,a=i-s,u=o(a),c=i<=l;s=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:r,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Ni=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Di=e=>(...t)=>v.asap(()=>e(...t)),fp=Ne.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ne.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ne.origin),Ne.navigator&&/(msie|trident)/i.test(Ne.navigator.userAgent)):()=>!0,dp=Ne.hasStandardBrowserEnv?{write(e,t,n,s,o,r){const i=[e+"="+encodeURIComponent(t)];v.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),v.isString(s)&&i.push("path="+s),v.isString(o)&&i.push("domain="+o),r===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function hp(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function pp(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Wa(e,t,n){let s=!hp(t);return e&&(s||n==!1)?pp(e,t):t}const Mi=e=>e instanceof ot?{...e}:e;function en(e,t){t=t||{};const n={};function s(u,c,f,h){return v.isPlainObject(u)&&v.isPlainObject(c)?v.merge.call({caseless:h},u,c):v.isPlainObject(c)?v.merge({},c):v.isArray(c)?c.slice():c}function o(u,c,f,h){if(v.isUndefined(c)){if(!v.isUndefined(u))return s(void 0,u,f,h)}else return s(u,c,f,h)}function r(u,c){if(!v.isUndefined(c))return s(void 0,c)}function i(u,c){if(v.isUndefined(c)){if(!v.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function l(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const a={url:r,method:r,data:r,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>o(Mi(u),Mi(c),f,!0)};return v.forEach(Object.keys({...e,...t}),function(c){const f=a[c]||o,h=f(e[c],t[c],c);v.isUndefined(h)&&f!==l||(n[c]=h)}),n}const za=e=>{const t=en({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:r,headers:i,auth:l}=t;t.headers=i=ot.from(i),t.url=ja(Wa(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(v.isFormData(n)){if(Ne.hasStandardBrowserEnv||Ne.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ne.hasStandardBrowserEnv&&(s&&v.isFunction(s)&&(s=s(t)),s||s!==!1&&fp(t.url))){const u=o&&r&&dp.read(r);u&&i.set(o,u)}return t},mp=typeof XMLHttpRequest<"u",gp=mp&&function(e){return new Promise(function(n,s){const o=za(e);let r=o.data;const i=ot.from(o.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=o,c,f,h,m,y;function S(){m&&m(),y&&y(),o.cancelToken&&o.cancelToken.unsubscribe(c),o.signal&&o.signal.removeEventListener("abort",c)}let _=new XMLHttpRequest;_.open(o.method.toUpperCase(),o.url,!0),_.timeout=o.timeout;function O(){if(!_)return;const C=ot.from("getAllResponseHeaders"in _&&_.getAllResponseHeaders()),U={data:!l||l==="text"||l==="json"?_.responseText:_.response,status:_.status,statusText:_.statusText,headers:C,config:e,request:_};Ka(function(q){n(q),S()},function(q){s(q),S()},U),_=null}"onloadend"in _?_.onloadend=O:_.onreadystatechange=function(){!_||_.readyState!==4||_.status===0&&!(_.responseURL&&_.responseURL.indexOf("file:")===0)||setTimeout(O)},_.onabort=function(){_&&(s(new ee("Request aborted",ee.ECONNABORTED,e,_)),_=null)},_.onerror=function(){s(new ee("Network Error",ee.ERR_NETWORK,e,_)),_=null},_.ontimeout=function(){let P=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const U=o.transitional||Va;o.timeoutErrorMessage&&(P=o.timeoutErrorMessage),s(new ee(P,U.clarifyTimeoutError?ee.ETIMEDOUT:ee.ECONNABORTED,e,_)),_=null},r===void 0&&i.setContentType(null),"setRequestHeader"in _&&v.forEach(i.toJSON(),function(P,U){_.setRequestHeader(U,P)}),v.isUndefined(o.withCredentials)||(_.withCredentials=!!o.withCredentials),l&&l!=="json"&&(_.responseType=o.responseType),u&&([h,y]=Ts(u,!0),_.addEventListener("progress",h)),a&&_.upload&&([f,m]=Ts(a),_.upload.addEventListener("progress",f),_.upload.addEventListener("loadend",m)),(o.cancelToken||o.signal)&&(c=C=>{_&&(s(!C||C.type?new yn(null,e,_):C),_.abort(),_=null)},o.cancelToken&&o.cancelToken.subscribe(c),o.signal&&(o.signal.aborted?c():o.signal.addEventListener("abort",c)));const T=ap(o.url);if(T&&Ne.protocols.indexOf(T)===-1){s(new ee("Unsupported protocol "+T+":",ee.ERR_BAD_REQUEST,e));return}_.send(r||null)})},yp=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,o;const r=function(u){if(!o){o=!0,l();const c=u instanceof Error?u:this.reason;s.abort(c instanceof ee?c:new yn(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,r(new ee(`timeout ${t} of ms exceeded`,ee.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(r):u.removeEventListener("abort",r)}),e=null)};e.forEach(u=>u.addEventListener("abort",r));const{signal:a}=s;return a.unsubscribe=()=>v.asap(l),a}},bp=yp,vp=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let s=0,o;for(;s<n;)o=s+t,yield e.slice(s,o),s=o},Sp=async function*(e,t){for await(const n of wp(e))yield*vp(n,t)},wp=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Fi=(e,t,n,s)=>{const o=Sp(e,t);let r=0,i,l=a=>{i||(i=!0,s&&s(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await o.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let h=r+=f;n(h)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),o.return()}},{highWaterMark:2})},eo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",qa=eo&&typeof ReadableStream=="function",_p=eo&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ja=(e,...t)=>{try{return!!e(...t)}catch{return!1}},xp=qa&&Ja(()=>{let e=!1;const t=new Request(Ne.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Hi=64*1024,Go=qa&&Ja(()=>v.isReadableStream(new Response("").body)),As={stream:Go&&(e=>e.body)};eo&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!As[t]&&(As[t]=v.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new ee(`Response type '${t}' is not supported`,ee.ERR_NOT_SUPPORT,s)})})})(new Response);const Cp=async e=>{if(e==null)return 0;if(v.isBlob(e))return e.size;if(v.isSpecCompliantForm(e))return(await new Request(Ne.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(v.isArrayBufferView(e)||v.isArrayBuffer(e))return e.byteLength;if(v.isURLSearchParams(e)&&(e=e+""),v.isString(e))return(await _p(e)).byteLength},Ep=async(e,t)=>{const n=v.toFiniteNumber(e.getContentLength());return n??Cp(t)},Rp=eo&&(async e=>{let{url:t,method:n,data:s,signal:o,cancelToken:r,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:h}=za(e);u=u?(u+"").toLowerCase():"text";let m=bp([o,r&&r.toAbortSignal()],i),y;const S=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let _;try{if(a&&xp&&n!=="get"&&n!=="head"&&(_=await Ep(c,s))!==0){let U=new Request(t,{method:"POST",body:s,duplex:"half"}),X;if(v.isFormData(s)&&(X=U.headers.get("content-type"))&&c.setContentType(X),U.body){const[q,K]=Ni(_,Ts(Di(a)));s=Fi(U.body,Hi,q,K)}}v.isString(f)||(f=f?"include":"omit");const O="credentials"in Request.prototype;y=new Request(t,{...h,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:O?f:void 0});let T=await fetch(y,h);const C=Go&&(u==="stream"||u==="response");if(Go&&(l||C&&S)){const U={};["status","statusText","headers"].forEach(N=>{U[N]=T[N]});const X=v.toFiniteNumber(T.headers.get("content-length")),[q,K]=l&&Ni(X,Ts(Di(l),!0))||[];T=new Response(Fi(T.body,Hi,q,()=>{K&&K(),S&&S()}),U)}u=u||"text";let P=await As[v.findKey(As,u)||"text"](T,e);return!C&&S&&S(),await new Promise((U,X)=>{Ka(U,X,{data:P,headers:ot.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:y})})}catch(O){throw S&&S(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new ee("Network Error",ee.ERR_NETWORK,e,y),{cause:O.cause||O}):ee.from(O,O&&O.code,e,y)}}),Ko={http:Uh,xhr:gp,fetch:Rp};v.forEach(Ko,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ui=e=>`- ${e}`,Tp=e=>v.isFunction(e)||e===null||e===!1,Xa={getAdapter:e=>{e=v.isArray(e)?e:[e];const{length:t}=e;let n,s;const o={};for(let r=0;r<t;r++){n=e[r];let i;if(s=n,!Tp(n)&&(s=Ko[(i=String(n)).toLowerCase()],s===void 0))throw new ee(`Unknown adapter '${i}'`);if(s)break;o[i||"#"+r]=s}if(!s){const r=Object.entries(o).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?r.length>1?`since :
`+r.map(Ui).join(`
`):" "+Ui(r[0]):"as no adapter specified";throw new ee("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:Ko};function wo(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new yn(null,e)}function Bi(e){return wo(e),e.headers=ot.from(e.headers),e.data=So.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Xa.getAdapter(e.adapter||vr.adapter)(e).then(function(s){return wo(e),s.data=So.call(e,e.transformResponse,s),s.headers=ot.from(s.headers),s},function(s){return Ga(s)||(wo(e),s&&s.response&&(s.response.data=So.call(e,e.transformResponse,s.response),s.response.headers=ot.from(s.response.headers))),Promise.reject(s)})}const Ya="1.11.0",to={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{to[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const ji={};to.transitional=function(t,n,s){function o(r,i){return"[Axios v"+Ya+"] Transitional option '"+r+"'"+i+(s?". "+s:"")}return(r,i,l)=>{if(t===!1)throw new ee(o(i," has been removed"+(n?" in "+n:"")),ee.ERR_DEPRECATED);return n&&!ji[i]&&(ji[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(r,i,l):!0}};to.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Ap(e,t,n){if(typeof e!="object")throw new ee("options must be an object",ee.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let o=s.length;for(;o-- >0;){const r=s[o],i=t[r];if(i){const l=e[r],a=l===void 0||i(l,r,e);if(a!==!0)throw new ee("option "+r+" must be "+a,ee.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new ee("Unknown option "+r,ee.ERR_BAD_OPTION)}}const hs={assertOptions:Ap,validators:to},ut=hs.validators;class Ps{constructor(t){this.defaults=t||{},this.interceptors={request:new Li,response:new Li}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const r=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?r&&!String(s.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+r):s.stack=r}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=en(this.defaults,n);const{transitional:s,paramsSerializer:o,headers:r}=n;s!==void 0&&hs.assertOptions(s,{silentJSONParsing:ut.transitional(ut.boolean),forcedJSONParsing:ut.transitional(ut.boolean),clarifyTimeoutError:ut.transitional(ut.boolean)},!1),o!=null&&(v.isFunction(o)?n.paramsSerializer={serialize:o}:hs.assertOptions(o,{encode:ut.function,serialize:ut.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),hs.assertOptions(n,{baseUrl:ut.spelling("baseURL"),withXsrfToken:ut.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=r&&v.merge(r.common,r[n.method]);r&&v.forEach(["delete","get","head","post","put","patch","common"],y=>{delete r[y]}),n.headers=ot.concat(i,r);const l=[];let a=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(n)===!1||(a=a&&S.synchronous,l.unshift(S.fulfilled,S.rejected))});const u=[];this.interceptors.response.forEach(function(S){u.push(S.fulfilled,S.rejected)});let c,f=0,h;if(!a){const y=[Bi.bind(this),void 0];for(y.unshift(...l),y.push(...u),h=y.length,c=Promise.resolve(n);f<h;)c=c.then(y[f++],y[f++]);return c}h=l.length;let m=n;for(f=0;f<h;){const y=l[f++],S=l[f++];try{m=y(m)}catch(_){S.call(this,_);break}}try{c=Bi.call(this,m)}catch(y){return Promise.reject(y)}for(f=0,h=u.length;f<h;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=en(this.defaults,t);const n=Wa(t.baseURL,t.url,t.allowAbsoluteUrls);return ja(n,t.params,t.paramsSerializer)}}v.forEach(["delete","get","head","options"],function(t){Ps.prototype[t]=function(n,s){return this.request(en(s||{},{method:t,url:n,data:(s||{}).data}))}});v.forEach(["post","put","patch"],function(t){function n(s){return function(r,i,l){return this.request(en(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:r,data:i}))}}Ps.prototype[t]=n(),Ps.prototype[t+"Form"]=n(!0)});const ps=Ps;class Sr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(r){n=r});const s=this;this.promise.then(o=>{if(!s._listeners)return;let r=s._listeners.length;for(;r-- >0;)s._listeners[r](o);s._listeners=null}),this.promise.then=o=>{let r;const i=new Promise(l=>{s.subscribe(l),r=l}).then(o);return i.cancel=function(){s.unsubscribe(r)},i},t(function(r,i,l){s.reason||(s.reason=new yn(r,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Sr(function(o){t=o}),cancel:t}}}const Pp=Sr;function Op(e){return function(n){return e.apply(null,n)}}function Ip(e){return v.isObject(e)&&e.isAxiosError===!0}const Wo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Wo).forEach(([e,t])=>{Wo[t]=e});const Lp=Wo;function Qa(e){const t=new ps(e),n=Pa(ps.prototype.request,t);return v.extend(n,ps.prototype,t,{allOwnKeys:!0}),v.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return Qa(en(e,o))},n}const Re=Qa(vr);Re.Axios=ps;Re.CanceledError=yn;Re.CancelToken=Pp;Re.isCancel=Ga;Re.VERSION=Ya;Re.toFormData=Qs;Re.AxiosError=ee;Re.Cancel=Re.CanceledError;Re.all=function(t){return Promise.all(t)};Re.spread=Op;Re.isAxiosError=Ip;Re.mergeConfig=en;Re.AxiosHeaders=ot;Re.formToJSON=e=>$a(v.isHTMLForm(e)?new FormData(e):e);Re.getAdapter=Xa.getAdapter;Re.HttpStatusCode=Lp;Re.default=Re;const kp=Re,It=(e,t="")=>({VITE_API_BASE_URL:"http://**************:8080/api",VITE_FRONTEND_URL:"http://**************",VITE_BACKEND_URL:"http://**************:8080",VITE_APP_NAME:"连连看游戏",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"production",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1})[e]||t,Os={api:{baseURL:It("VITE_API_BASE_URL","http://localhost:8080/api"),timeout:1e4},domain:{frontend:It("VITE_FRONTEND_URL","http://localhost:5173"),backend:It("VITE_BACKEND_URL","http://localhost:8080")},app:{name:It("VITE_APP_NAME","连连看游戏"),version:It("VITE_APP_VERSION","1.0.0"),env:It("VITE_NODE_ENV","development")},isDev:It("VITE_NODE_ENV","development")==="development",isProd:It("VITE_NODE_ENV","development")==="production"};Os.isDev&&console.log("🔧 应用配置信息:",Os);const je=kp.create({baseURL:Os.api.baseURL,timeout:Os.api.timeout});je.interceptors.request.use(e=>e,e=>Promise.reject(e));je.interceptors.response.use(e=>e.data,e=>(console.error("请求错误:",e),Promise.reject(e)));const St={createGameSession(e){return je.post("/session/create",{playerName:e})},getGameSessionState(e){return je.get(`/session/${e}`)},updateGameSessionState(e,t,n,s){return je.post("/session/update",{sessionId:e,boardState:t,score:n,isCompleted:s})},getDefaultBoardState(){return je.get("/game/board")},getBoardStateByVersion(e){return je.get(`/game/board/${e}`)},getBoardVersions(){return je.get("/game/versions")},getBoardData(e=1){return je.get(`/board/${e}`)},saveGameProgress(e,t){return je.post("/game/save",{boardState:e,playerName:t})},loadGameProgress(e){return je.post("/game/load",{progressCode:e})},resetGame(){return je.post("/game/reset")},exportProgress(e){return je.post("/progress/export",{boardState:e})},importProgress(e){return je.post("/progress/import",{progressCode:e})}};function Np(e,t,n){if(e.itemId!==t.itemId||e.id===t.id)return null;const s=n.length,o=n[0].length;if(Vi(e)||Vi(t))return Dp(e,t,n,o,s);const r=Gi(e),i=Gi(t);for(const l of r)for(const a of i){const u=ec(l,a,n,o,s,e,t);if(u)return{path:u,from:e,to:t}}return null}function Vi(e){return e.width>1||e.height>1}function Dp(e,t,n,s,o){const r={x:e.x,y:e.y},i={x:t.x,y:t.y},l=ec(r,i,n,s,o,e,t);return!l||!Mp(e,l,n,t)?null:{path:l,from:e,to:t}}function Mp(e,t,n,s){for(let o=0;o<t.length;o++){const r=t[o],i=$i(e,r);if(!Za(e,i,n,s))return!1;if(o<t.length-1){const l=t[o+1],a=$i(e,l);if(!Fp(e,i,a,n,s))return!1}}return!0}function $i(e,t){return{x:t.x,y:t.y}}function Fp(e,t,n,s,o){const r=n.x-t.x,i=n.y-t.y,l=Math.max(Math.abs(r),Math.abs(i));if(l===0)return!0;for(let a=0;a<=l;a++){const u=a/l,c={x:Math.round(t.x+r*u),y:Math.round(t.y+i*u)};if(!Za(e,c,s,o))return!1}return!0}function Za(e,t,n,s){for(let o=0;o<e.height;o++)for(let r=0;r<e.width;r++){const i=t.x+r,l=t.y+o;if(i<0||i>=n[0].length||l<0||l>=n.length)return!1;const a=n[l][i];if(a&&!a.isRemoved&&a.id!==e.id&&a.id!==s.id)return!1}return!0}function Gi(e){const t=[];for(let n=0;n<e.height;n++)for(let s=0;s<e.width;s++)t.push({x:e.x+s,y:e.y+n});return t}function ec(e,t,n,s,o,r,i){let l=tt(e,t,n,s,o,r,i);return l||(l=Hp(e,t,n,s,o,r,i),l)||(l=Up(e,t,n,s,o,r,i),l)?l:null}function tt(e,t,n,s,o,r,i){if(e.y===t.y){const l=Math.min(e.x,t.x),a=Math.max(e.x,t.x);for(let u=l;u<=a;u++)if(!Ht(u,e.y,r,n,i))return null;return[e,t]}if(e.x===t.x){const l=Math.min(e.y,t.y),a=Math.max(e.y,t.y);for(let u=l;u<=a;u++)if(!Ht(e.x,u,r,n,i))return null;return[e,t]}return null}function Hp(e,t,n,s,o,r,i){const l={x:e.x,y:t.y};if(Ht(l.x,l.y,r,n,i)){const u=tt(e,l,n,s,o,r,i),c=tt(l,t,n,s,o,r,i);if(u&&c)return[e,l,t]}const a={x:t.x,y:e.y};if(Ht(a.x,a.y,r,n,i)){const u=tt(e,a,n,s,o,r,i),c=tt(a,t,n,s,o,r,i);if(u&&c)return[e,a,t]}return null}function Up(e,t,n,s,o,r,i){for(let l=0;l<s;l++){if(l===e.x)continue;const a={x:l,y:e.y},u={x:l,y:t.y};if(Ht(a.x,a.y,r,n,i)&&Ht(u.x,u.y,r,n,i)){const c=tt(e,a,n,s,o,r,i),f=tt(a,u,n,s,o,r,i),h=tt(u,t,n,s,o,r,i);if(c&&f&&h)return[e,a,u,t]}}for(let l=0;l<o;l++){if(l===e.y)continue;const a={x:e.x,y:l},u={x:t.x,y:l};if(Ht(a.x,a.y,r,n,i)&&Ht(u.x,u.y,r,n,i)){const c=tt(e,a,n,s,o,r,i),f=tt(a,u,n,s,o,r,i),h=tt(u,t,n,s,o,r,i);if(c&&f&&h)return[e,a,u,t]}}return null}function Ht(e,t,n,s,o){for(let r=0;r<n.height;r++)for(let i=0;i<n.width;i++){const l=e+i,a=t+r;if(l<0||l>=s[0].length||a<0||a>=s.length)return!1;const u=s[a][l];if(u&&!u.isRemoved&&u.id!==n.id&&u.id!==o.id)return!1}return!0}const tc=ma("game",{state:()=>({sessionId:null,playerName:"",boardState:null,score:0,isCompleted:!1,availableVersions:[],currentVersion:"simple",isLoading:!1,selectedItems:[],connectionLine:null,removingItems:[],startTime:null,gameTime:0,gameHistory:[],maxHistorySize:15,canUndo:!1,storedRed:{red6:0,red9:0,red12:0},pendingDatabaseUpdate:!1}),getters:{boardGrid(){var t;if(!this.boardState)return[];const e=Array(this.boardState.height).fill(null).map(()=>Array(this.boardState.width).fill(null));return(t=this.boardState.items)==null||t.forEach(n=>{if(!n.isRemoved)for(let s=0;s<n.height;s++)for(let o=0;o<n.width;o++)n.y+s<this.boardState.height&&n.x+o<this.boardState.width&&(e[n.y+s][n.x+o]=n)}),e},activeItems(){var e,t;return((t=(e=this.boardState)==null?void 0:e.items)==null?void 0:t.filter(n=>!n.isRemoved))||[]},isGameCompleted(){return this.activeItems.length===0},undoStepsCount(){return this.gameHistory.length},lastMoveDescription(){return this.gameHistory.length===0?"":this.gameHistory[this.gameHistory.length-1].description||""}},actions:{async loadAvailableVersions(){try{const e=await St.getBoardVersions();e.code===200&&(this.availableVersions=e.data)}catch{}},async switchVersion(e){try{this.isLoading=!0;const t=await St.getBoardStateByVersion(e);if(t.code===200)return this.currentVersion=e,this.boardState=t.data,this.selectedItems=[],this.score=0,this.isCompleted=!1,this.clearUndoHistory(),this.clearStoredRed(),this.pendingDatabaseUpdate=!1,!0;throw new Error(t.message)}catch(t){throw t}finally{this.isLoading=!1}},async createNewGame(e){try{this.isLoading=!0,this.playerName=e;const t=await St.createGameSession(e);if(t.code===200)return this.sessionId=t.data.sessionId,this.boardState=t.data.boardState,this.startTime=new Date(t.data.startTime),this.score=0,this.isCompleted=!1,this.selectedItems=[],this.clearUndoHistory(),this.clearStoredRed(),this.pendingDatabaseUpdate=!1,!0;throw new Error(t.message)}catch(t){throw console.error("创建游戏失败:",t),t}finally{this.isLoading=!1}},async loadGameSession(e){try{this.isLoading=!0;const t=await St.getGameSessionState(e);if(t.code===200)return this.sessionId=t.data.sessionId,this.playerName=t.data.playerName,this.boardState=t.data.boardState,this.score=t.data.score,this.isCompleted=t.data.isCompleted,this.startTime=new Date(t.data.startTime),this.selectedItems=[],t.data.boardState.storedRed?this.storedRed=t.data.boardState.storedRed:this.clearStoredRed(),!0;throw new Error(t.message)}catch(t){throw console.error("加载游戏会话失败:",t),t}finally{this.isLoading=!1}},async updateGameState(){if(this.sessionId)try{const e={...this.boardState,storedRed:this.storedRed};await St.updateGameSessionState(this.sessionId,e,this.score,this.isCompleted),this.pendingDatabaseUpdate=!1}catch(e){console.error("更新游戏状态失败:",e)}},saveGameState(e=""){const t={boardState:JSON.parse(JSON.stringify(this.boardState)),score:this.score,selectedItems:[...this.selectedItems],storedRed:JSON.parse(JSON.stringify(this.storedRed)),timestamp:Date.now(),description:e};this.gameHistory.push(t),this.gameHistory.length>this.maxHistorySize&&this.gameHistory.shift(),this.canUndo=this.gameHistory.length>0},undoLastMove(){if(this.gameHistory.length===0)return console.warn("没有可撤销的操作"),!1;const e=this.gameHistory.pop();return this.boardState=e.boardState,this.score=e.score,this.selectedItems=e.selectedItems,e.storedRed&&(this.storedRed=e.storedRed),this.connectionLine=null,this.removingItems=[],this.canUndo=this.gameHistory.length>0,this.pendingDatabaseUpdate=!0,!0},selectItem(e){this.selectedItems.length>=2&&(this.selectedItems=[]);const t=this.selectedItems.findIndex(n=>n.id===e.id);t>=0?this.selectedItems.splice(t,1):(this.selectedItems.push(e),this.selectedItems.length===2&&this.checkConnection())},checkConnection(){if(this.selectedItems.length!==2)return;const[e,t]=this.selectedItems,n=Np(e,t,this.boardGrid);if(n){const s=`消除了${e.name||"物品"}和${t.name||"物品"}`;this.saveGameState(s),this.showConnectionAnimation(n.path),setTimeout(()=>{this.showRemoveAnimation([e,t]),setTimeout(()=>{this.removeItems([e,t]),this.score+=this.calculateScore(e,t),this.pendingDatabaseUpdate=!0,this.isGameCompleted&&(this.isCompleted=!0,this.clearUndoHistory(),this.updateGameState()),this.clearSelection()},800)},800)}else this.showConnectionFailed(),this.clearSelection()},isLargeItem(e){return e.width>1||e.height>1},calculateScore(e,t){let n=10;return(this.isLargeItem(e)||this.isLargeItem(t))&&(n+=20),n},showConnectionAnimation(e){this.connectionLine={path:e,timestamp:Date.now()},this.forceRefreshCallback&&setTimeout(()=>{this.forceRefreshCallback()},10),setTimeout(()=>{this.connectionLine=null},800)},setForceRefreshCallback(e){this.forceRefreshCallback=e},showRemoveAnimation(e){this.removingItems=[...e],setTimeout(()=>{this.removingItems=[]},800)},showConnectionFailed(){},clearSelection(){setTimeout(()=>{this.selectedItems=[]},500)},removeItems(e){e.forEach(t=>{const n=this.boardState.items.find(s=>s.id===t.id);n&&(n.isRemoved=!0)})},async resetGame(){if(this.sessionId)try{const e=await St.getDefaultBoardState();e.code===200&&(this.boardState=e.data,this.score=0,this.isCompleted=!1,this.selectedItems=[],this.clearUndoHistory(),this.updateGameState())}catch(e){console.error("重置游戏失败:",e)}},async exportGameProgress(){if(!this.sessionId||!this.boardState)throw new Error("没有可导出的游戏数据");try{const e={...this.boardState,storedRed:this.storedRed},t=await St.exportProgress(e);if(t.code===200)return t.data.progressCode;throw new Error(t.message)}catch(e){throw console.error("导出游戏进度失败:",e),e}},async importGameProgress(e){try{const t=await St.importProgress(e);if(t.code===200)return this.boardState=t.data.boardState,this.selectedItems=[],this.connectionLine=null,this.removingItems=[],t.data.boardState.storedRed?this.storedRed=t.data.boardState.storedRed:this.clearStoredRed(),this.clearUndoHistory(),this.sessionId&&await this.updateGameState(),!0;throw new Error(t.message)}catch(t){throw console.error("导入游戏进度失败:",t),t}},async saveCurrentProgress(){if(!this.sessionId||!this.boardState)throw new Error("没有可保存的游戏数据");try{const e={...this.boardState,storedRed:this.storedRed},t=await St.saveGameProgress(e,this.playerName);if(t.code===200)return await this.updateGameState(),t.data.progressCode;throw new Error(t.message)}catch(e){throw console.error("保存游戏进度失败:",e),e}},clearUndoHistory(){this.gameHistory=[],this.canUndo=!1},increaseStoredRed(e){this.storedRed.hasOwnProperty(e)&&(this.storedRed[e]++,this.pendingDatabaseUpdate=!0)},decreaseStoredRed(e){this.storedRed.hasOwnProperty(e)&&this.storedRed[e]>0&&(this.storedRed[e]--,this.pendingDatabaseUpdate=!0)},setStoredRed(e,t){this.storedRed.hasOwnProperty(e)&&t>=0&&(this.storedRed[e]=t,this.pendingDatabaseUpdate=!0)},clearStoredRed(){this.storedRed={red6:0,red9:0,red12:0},this.pendingDatabaseUpdate=!0},clearGame(){this.sessionId=null,this.playerName="",this.boardState=null,this.score=0,this.isCompleted=!1,this.selectedItems=[],this.connectionLine=null,this.removingItems=[],this.startTime=null,this.gameTime=0,this.clearUndoHistory(),this.pendingDatabaseUpdate=!1}}});const Bp={name:"Home",setup(){const e=Aa(),t=tc(),n=ge(!1),s=ge(!1),o=ge(!1),r=ge(""),i=ge(""),l=ge(!1),a=()=>{n.value=!0},u=()=>{n.value=!1,s.value=!0},c=()=>{n.value=!1},f=()=>{e.push("/wheel")},h=async()=>{if(r.value.trim())try{l.value=!0,await t.createNewGame(r.value.trim()),e.push(`/game/${t.sessionId}`)}catch(S){alert("创建游戏失败: "+S.message)}finally{l.value=!1,y()}},m=async()=>{if(i.value.trim())try{l.value=!0,await t.createNewGame("临时玩家"),await t.importGameProgress(i.value.trim()),e.push(`/game/${t.sessionId}`)}catch(S){alert("加载游戏失败: "+S.message)}finally{l.value=!1,y()}},y=()=>{s.value=!1,o.value=!1,r.value="",i.value=""};return{showLianLianKanDialog:n,showNameDialog:s,showLoadDialog:o,playerName:r,progressCode:i,isLoading:l,startLianLianKan:a,startNewLianLianKan:u,closeLianLianKanDialog:c,createGame:h,loadGame:m,closeDialog:y,goToWheelGame:f}}},jp={class:"home"},Vp={class:"container"},$p={class:"game-cards"},Gp={class:"game-options"},Kp={class:"dialog-buttons"},Wp={class:"dialog-buttons"},zp=["disabled"],qp={class:"dialog-buttons"},Jp=["disabled"],Xp={key:3,class:"loading"};function Yp(e,t,n,s,o,r){return z(),J("div",jp,[g("div",Vp,[g("div",$p,[g("div",{class:"game-card",onClick:t[0]||(t[0]=(...i)=>s.startLianLianKan&&s.startLianLianKan(...i))},t[19]||(t[19]=[cs('<div class="card-header" data-v-65f14ec2><div class="card-status available" data-v-65f14ec2>可玩</div></div><div class="card-content" data-v-65f14ec2><h3 data-v-65f14ec2>连连看</h3><p data-v-65f14ec2>经典的连连看消除游戏，考验你的观察力和策略思维</p></div><div class="card-footer" data-v-65f14ec2><button class="play-btn" data-v-65f14ec2>开始游戏</button></div>',3)])),g("div",{class:"game-card",onClick:t[1]||(t[1]=(...i)=>s.goToWheelGame&&s.goToWheelGame(...i))},t[20]||(t[20]=[cs('<div class="card-header" data-v-65f14ec2><div class="card-status available" data-v-65f14ec2>可玩</div></div><div class="card-content" data-v-65f14ec2><h3 data-v-65f14ec2> 幸运转盘</h3><p data-v-65f14ec2>全新的转盘游戏</p></div><div class="card-footer" data-v-65f14ec2><button class="play-btn" data-v-65f14ec2>开始游戏</button></div>',3)])),t[21]||(t[21]=cs('<div class="game-card coming-soon" data-v-65f14ec2><div class="card-header" data-v-65f14ec2><div class="card-status coming-soon" data-v-65f14ec2>即将推出</div></div><div class="card-content" data-v-65f14ec2><h3 data-v-65f14ec2>连连看升级版</h3><p data-v-65f14ec2>敬请期待</p></div><div class="card-footer" data-v-65f14ec2><button class="play-btn" disabled data-v-65f14ec2>敬请期待</button></div></div>',1))]),s.showLianLianKanDialog?(z(),J("div",{key:0,class:"dialog-overlay",onClick:t[6]||(t[6]=(...i)=>s.closeLianLianKanDialog&&s.closeLianLianKanDialog(...i))},[g("div",{class:"dialog game-options-dialog",onClick:t[5]||(t[5]=In(()=>{},["stop"]))},[t[24]||(t[24]=g("h3",null,"连连看游戏",-1)),t[25]||(t[25]=g("p",{class:"dialog-subtitle"},"选择游戏模式",-1)),g("div",Gp,[g("div",{class:"option-card",onClick:t[2]||(t[2]=(...i)=>s.startNewLianLianKan&&s.startNewLianLianKan(...i))},t[22]||(t[22]=[g("div",{class:"option-icon"},"🆕",-1),g("h4",null,"开始新游戏",-1),g("p",null,"开始一局全新的连连看游戏",-1)])),g("div",{class:"option-card",onClick:t[3]||(t[3]=i=>s.showLoadDialog=!0)},t[23]||(t[23]=[g("div",{class:"option-icon"},"📂",-1),g("h4",null,"加载游戏",-1),g("p",null,"输入进度代码继续之前的游戏",-1)]))]),g("div",Kp,[g("button",{onClick:t[4]||(t[4]=(...i)=>s.closeLianLianKanDialog&&s.closeLianLianKanDialog(...i)),class:"btn-cancel"},"取消")])])])):De("",!0),s.showNameDialog?(z(),J("div",{key:1,class:"dialog-overlay",onClick:t[12]||(t[12]=(...i)=>s.closeDialog&&s.closeDialog(...i))},[g("div",{class:"dialog",onClick:t[11]||(t[11]=In(()=>{},["stop"]))},[t[26]||(t[26]=g("h3",null,"输入玩家名称",-1)),Hn(g("input",{"onUpdate:modelValue":t[7]||(t[7]=i=>s.playerName=i),type:"text",placeholder:"请输入您的名称",onKeyup:t[8]||(t[8]=Do((...i)=>s.createGame&&s.createGame(...i),["enter"])),maxlength:"20"},null,544),[[Rs,s.playerName]]),g("div",Wp,[g("button",{onClick:t[9]||(t[9]=(...i)=>s.closeDialog&&s.closeDialog(...i)),class:"btn-cancel"},"取消"),g("button",{onClick:t[10]||(t[10]=(...i)=>s.createGame&&s.createGame(...i)),class:"btn-confirm",disabled:!s.playerName.trim()}," 开始游戏 ",8,zp)])])])):De("",!0),s.showLoadDialog?(z(),J("div",{key:2,class:"dialog-overlay",onClick:t[18]||(t[18]=(...i)=>s.closeDialog&&s.closeDialog(...i))},[g("div",{class:"dialog",onClick:t[17]||(t[17]=In(()=>{},["stop"]))},[t[27]||(t[27]=g("h3",null,"加载连连看游戏",-1)),Hn(g("input",{"onUpdate:modelValue":t[13]||(t[13]=i=>s.progressCode=i),type:"text",placeholder:"请输入进度代码",onKeyup:t[14]||(t[14]=Do((...i)=>s.loadGame&&s.loadGame(...i),["enter"])),maxlength:"50"},null,544),[[Rs,s.progressCode]]),g("div",qp,[g("button",{onClick:t[15]||(t[15]=(...i)=>s.closeDialog&&s.closeDialog(...i)),class:"btn-cancel"},"取消"),g("button",{onClick:t[16]||(t[16]=(...i)=>s.loadGame&&s.loadGame(...i)),class:"btn-confirm",disabled:!s.progressCode.trim()}," 加载游戏 ",8,Jp)])])])):De("",!0),s.isLoading?(z(),J("div",Xp,t[28]||(t[28]=[g("div",{class:"spinner"},null,-1),g("p",null,"正在创建游戏...",-1)]))):De("",!0)])])}const Qp=tn(Bp,[["render",Yp],["__scopeId","data-v-65f14ec2"]]);function Zp(){return navigator&&navigator.clipboard&&typeof navigator.clipboard.writeText=="function"}function em(){return window.isSecureContext||location.protocol==="https:"||location.hostname==="localhost"}function Ki(e){try{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="2em",t.style.height="2em",t.style.padding="0",t.style.border="none",t.style.outline="none",t.style.boxShadow="none",t.style.background="transparent",t.style.opacity="0",document.body.appendChild(t),t.focus(),t.select();const n=document.execCommand("copy");return document.body.removeChild(t),n}catch(t){return console.error("传统复制方法失败:",t),!1}}async function tm(e){if(!e)return console.warn("复制的文本不能为空"),!1;if(Zp()&&em())try{return await navigator.clipboard.writeText(e),!0}catch(t){return console.warn("现代剪贴板API失败，尝试传统方法:",t),Ki(e)}return Ki(e)}function nm(e,t="已复制到剪贴板",n="复制失败，请手动复制"){alert(e?t:n)}async function sm(e,t="代码已复制到剪贴板",n="复制失败，请手动复制代码"){const s=await tm(e);return nm(s,t,n),s}const om={name:"ConnectionLine",props:{connectionLine:{type:Object,default:null},cellSize:{type:Number,default:60},boardOffset:{type:Object,default:()=>({x:0,y:0})},boardPadding:{type:Number,default:32}},setup(e){const t=we(()=>window.innerWidth),n=we(()=>window.innerHeight),s=ge(0),o=we(()=>{var f,h,m;if(!((f=e.connectionLine)!=null&&f.path))return[];s.value;const i=((h=e.boardOffset)==null?void 0:h.x)||0,l=((m=e.boardOffset)==null?void 0:m.y)||0,a=e.boardPadding||32,u=e.cellSize||60;return e.connectionLine.path.map(y=>({x:i+a+(y.x+.5)*u,y:l+a+(y.y+.5)*u}))});pt(()=>e.connectionLine,async i=>{i&&(s.value++,await Ct(),setTimeout(()=>{s.value++},50))},{immediate:!0});const r=we(()=>o.value.map(i=>`${i.x},${i.y}`).join(" "));return{svgWidth:t,svgHeight:n,pathPointsArray:o,pathPoints:r,recalcTrigger:s}}},rm={key:0,class:"connection-overlay"},im=["width","height"],lm=["points"],am=["cx","cy"];function cm(e,t,n,s,o,r){return n.connectionLine?(z(),J("div",rm,[(z(),J("svg",{class:"connection-svg",width:s.svgWidth,height:s.svgHeight},[g("polyline",{points:s.pathPoints,class:"connection-path",fill:"none",stroke:"#ff9800","stroke-width":"3","stroke-dasharray":"5,5"},t[0]||(t[0]=[g("animate",{attributeName:"stroke-dashoffset",values:"0;10",dur:"0.5s",repeatCount:"indefinite"},null,-1)]),8,lm),(z(!0),J(ve,null,gt(s.pathPointsArray,(i,l)=>(z(),J("circle",{key:l,cx:i.x,cy:i.y,r:"4",fill:"#ff9800",class:"path-point"},t[1]||(t[1]=[g("animate",{attributeName:"r",values:"4;8;4",dur:"0.8s",begin:"0s"},null,-1)]),8,am))),128))],8,im))])):De("",!0)}const um=tn(om,[["render",cm],["__scopeId","data-v-b8baa436"]]);const fm={name:"RemoveAnimation",props:{removingItems:{type:Array,default:()=>[]},cellSize:{type:Number,default:60},boardOffset:{type:Object,default:()=>({x:0,y:0})},boardPadding:{type:Number,default:32}},setup(e){return{getItemPosition:o=>{const r=e.boardOffset.x+e.boardPadding+o.x*e.cellSize,i=e.boardOffset.y+e.boardPadding+o.y*e.cellSize;return{position:"absolute",left:`${r}px`,top:`${i}px`,width:`${o.width*e.cellSize}px`,height:`${o.height*e.cellSize}px`,zIndex:1e3}},getParticleStyle:o=>{const r=o*45*Math.PI/180,i=30,l=Math.cos(r)*i,a=Math.sin(r)*i;return{"--end-x":`${l}px`,"--end-y":`${a}px`,animationDelay:`${o*.05}s`}},getItemImage:o=>`/images/${o.name}.png`}}},dm={key:0,class:"remove-animation-overlay"},hm={class:"explosion-effect"},pm={class:"item-fade"},mm=["src","alt"];function gm(e,t,n,s,o,r){return n.removingItems.length>0?(z(),J("div",dm,[(z(!0),J(ve,null,gt(n.removingItems,i=>(z(),J("div",{key:i.id,class:"removing-item",style:Et(s.getItemPosition(i))},[g("div",hm,[(z(),J(ve,null,gt(8,l=>g("div",{class:"particle",key:l,style:Et(s.getParticleStyle(l))},null,4)),64))]),g("div",pm,[g("img",{src:s.getItemImage(i),alt:i.name,class:"item-image"},null,8,mm)])],4))),128))])):De("",!0)}const ym=tn(fm,[["render",gm],["__scopeId","data-v-78780699"]]);const bm={name:"Game",components:{ConnectionLine:um,RemoveAnimation:ym},props:{sessionId:String},setup(e){const t=Jd(),n=Aa(),s=tc(),o=ge(!1),r=ge(!1),i=ge(""),l=ge(""),a=ge(null),u=ge(null),c=ge(window.innerWidth),f=ge(window.innerHeight),h=ge(0),m=we(()=>{if(h.value,!u.value)return{x:0,y:0};const I=u.value.getBoundingClientRect();return{x:I.left,y:I.top}}),y=we(()=>{var Pe,$e;const I=((Pe=s.boardState)==null?void 0:Pe.width)||10,re=(($e=s.boardState)==null?void 0:$e.height)||9;if(c.value<=768){const Ee=c.value-40,Ue=Math.floor(Ee/I)-2;return I>=15||re>=15?Math.max(Math.min(35,Ue),25):I>=10||re>=9?Math.max(Math.min(50,Ue),35):Math.max(Math.min(60,Ue),40)}else{let Ee;c.value<=1600?Ee=c.value-100:Ee=c.value-650;const Ue=Math.floor(Ee/I)-4;return I>=15||re>=15?Math.max(Math.min(55,Ue),35):I>=10||re>=9?Math.max(Math.min(70,Ue),50):Math.max(Math.min(80,Ue),60)}}),S=we(()=>c.value<=768?16:32),_=()=>{c.value=window.innerWidth,f.value=window.innerHeight,h.value++},O=()=>{h.value++},T=I=>{I.ctrlKey&&I.key==="z"&&s.canUndo&&(I.preventDefault(),te())};pt(()=>s.boardState,async()=>{s.boardState&&(await Ct(),h.value++)},{deep:!0}),pt(()=>s.currentVersion,async()=>{await Ct(),setTimeout(()=>{h.value++},100)}),pt(()=>y.value,async()=>{await Ct(),setTimeout(()=>{h.value++},50)}),pt(()=>S.value,async()=>{await Ct(),setTimeout(()=>{h.value++},50)}),js(async()=>{await s.loadAvailableVersions();const I=e.sessionId||t.params.sessionId;if(I&&I!=="undefined")try{await s.loadGameSession(I),s.boardState&&(s.boardState.width===6&&s.boardState.height===6?s.currentVersion="simple":s.boardState.width===15&&s.boardState.height===15?s.currentVersion="hard":s.currentVersion="normal")}catch(re){console.error("加载游戏会话失败:",re),alert("游戏会话不存在或已过期，将返回首页"),n.push("/")}else try{await s.switchVersion("simple")}catch(re){console.error("加载默认版本失败:",re),n.push("/")}document.addEventListener("keydown",T),window.addEventListener("resize",_),window.addEventListener("scroll",O),await Ct(),h.value++,s.setForceRefreshCallback(()=>{h.value++})}),lr(()=>{document.removeEventListener("keydown",T),window.removeEventListener("resize",_),window.removeEventListener("scroll",O)});const C=(I,re,Pe)=>{!I||I.isRemoved||(I.width>1||I.height>1||I.x===re&&I.y===Pe)&&s.selectItem(I)},P=I=>I?s.selectedItems.some(re=>re.id===I.id):!1,U=I=>`/images/${I.name}.png`,X=I=>I.width===1&&I.height===1?{}:{width:`${I.width*y.value-2}px`,height:`${I.height*y.value-2}px`,position:"absolute",top:"1px",left:"1px",zIndex:10},q=I=>{const re=Math.max(y.value-10,20);return I.width===1&&I.height===1?{width:`${re}px`,height:`${re}px`}:{width:`${I.width*y.value-12}px`,height:`${I.height*y.value-12}px`,position:"absolute",top:"1px",left:"1px",zIndex:10}},K=async I=>{if(I!==s.currentVersion)try{await s.switchVersion(I)}catch(re){console.error("切换版本失败:",re),alert("切换版本失败: "+re.message)}},N=we(()=>s.availableVersions?[...s.availableVersions].sort((I,re)=>{const Pe={simple:1,normal:2,hard:3};return(Pe[I.version]||999)-(Pe[re.version]||999)}):[]),W=I=>({simple:"🟢",normal:"🟡",hard:"🔴"})[I]||"⚪",te=()=>{s.undoLastMove()},D=async()=>{confirm("确定要重置游戏吗？")&&await s.resetGame()},ne=async()=>{try{const I=await s.exportGameProgress();l.value=I,r.value=!0}catch(I){alert("保存失败: "+I.message)}},be=async()=>{if(!i.value.trim()){alert("请输入进度代码");return}try{await s.importGameProgress(i.value.trim()),alert("导入成功!"),ie()}catch(I){alert("导入失败: "+I.message)}},Te=async()=>{await sm(l.value,"代码已复制到剪贴板！","复制失败，请手动复制代码")},ie=()=>{o.value=!1,i.value=""};return{gameStore:s,showImportDialog:o,showSaveSuccess:r,importCode:i,savedCode:l,boardContainer:a,gameBoard:u,boardOffset:m,cellSize:y,boardPadding:S,selectItem:C,isSelected:P,getItemImage:U,getItemStyle:X,getItemImageStyle:q,selectVersion:K,sortedVersions:N,getVersionIcon:W,undoMove:te,resetGame:D,saveGame:ne,importGame:be,copySaveCode:Te,closeImportDialog:ie,backToHome:()=>{s.clearGame(),n.push("/")}}}},vm={class:"game-page"},Sm={class:"game-header"},wm={class:"player-info"},_m={class:"player-name"},xm={class:"game-controls"},Cm={class:"game-main"},Em={class:"game-board-section"},Rm={class:"board-announcement-container"},Tm={class:"version-selector-panel"},Am={class:"selector-card"},Pm={class:"selector-options"},Om=["onClick"],Im={class:"option-icon"},Lm={class:"option-info"},km={class:"option-name"},Nm={class:"option-size"},Dm={class:"game-board-container",ref:"boardContainer"},Mm={key:0,class:"game-board",ref:"gameBoard"},Fm=["onClick"],Hm={key:0,class:"game-item"},Um=["src","alt"],Bm={key:1,class:"loading-board"},jm={class:"game-bottom-controls"},Vm=["disabled","title"],$m={class:"right-panels"},Gm={class:"stored-red-panel"},Km={class:"stored-red-content"},Wm={class:"red-item"},zm=["disabled"],qm={class:"red-count"},Jm={class:"red-item"},Xm=["disabled"],Ym={class:"red-count"},Qm={class:"red-item"},Zm=["disabled"],eg={class:"red-count"},tg={key:0,class:"game-complete"},ng={class:"complete-dialog"},sg={class:"complete-buttons"},og={class:"dialog-buttons"},rg=["disabled"],ig={key:2,class:"save-success"},lg={class:"success-content"};function ag(e,t,n,s,o,r){const i=Ss("ConnectionLine"),l=Ss("RemoveAnimation");return z(),J(ve,null,[g("div",vm,[g("div",Sm,[g("div",wm,[g("span",_m,"玩家: "+ye(s.gameStore.playerName||"游客"),1)]),g("div",xm,[g("button",{onClick:t[0]||(t[0]=(...a)=>s.backToHome&&s.backToHome(...a)),class:"btn-control btn-back"},"返回首页")])]),g("div",Cm,[g("div",Em,[t[29]||(t[29]=g("div",{class:"game-title"},[g("h1",null,"小里连连看")],-1)),g("div",Rm,[g("div",Tm,[g("div",Am,[t[21]||(t[21]=g("div",{class:"selector-header"},[g("h3",null,"🎯 选择版本")],-1)),g("div",Pm,[(z(!0),J(ve,null,gt(s.sortedVersions,a=>(z(),J("div",{key:a.version,class:mt(["selector-option",{active:s.gameStore.currentVersion===a.version}]),onClick:u=>s.selectVersion(a.version)},[g("div",Im,ye(s.getVersionIcon(a.version)),1),g("div",Lm,[g("div",km,ye(a.name),1),g("div",Nm,ye(a.size),1)])],10,Om))),128))])])]),g("div",null,[g("div",Dm,[s.gameStore.boardState?(z(),J("div",Mm,[(z(!0),J(ve,null,gt(s.gameStore.boardGrid,(a,u)=>(z(),J("div",{key:u,class:"board-row"},[(z(!0),J(ve,null,gt(a,(c,f)=>(z(),J("div",{key:f,class:mt(["board-cell",{"has-item":c&&!c.isRemoved,selected:s.isSelected(c),"large-item":c&&(c.width>1||c.height>1),"main-cell":c&&c.x===f&&c.y===u}]),style:Et({width:s.cellSize+"px",height:s.cellSize+"px"}),onClick:h=>s.selectItem(c,f,u)},[c&&!c.isRemoved&&c.x===f&&c.y===u?(z(),J("div",Hm,[g("img",{src:s.getItemImage(c),alt:c.name,class:"item-image",style:Et(s.getItemImageStyle(c))},null,12,Um)])):De("",!0)],14,Fm))),128))]))),128))],512)):(z(),J("div",Bm,t[22]||(t[22]=[g("div",{class:"spinner"},null,-1),g("p",null,"加载游戏中...",-1)]))),Se(i,{"connection-line":s.gameStore.connectionLine,"cell-size":s.cellSize,"board-offset":s.boardOffset,"board-padding":s.boardPadding},null,8,["connection-line","cell-size","board-offset","board-padding"]),Se(l,{"removing-items":s.gameStore.removingItems,"cell-size":s.cellSize,"board-offset":s.boardOffset},null,8,["removing-items","cell-size","board-offset"])],512),g("div",jm,[g("button",{onClick:t[1]||(t[1]=(...a)=>s.undoMove&&s.undoMove(...a)),class:"btn-control btn-undo",disabled:!s.gameStore.canUndo,title:s.gameStore.canUndo?`撤销: ${s.gameStore.lastMoveDescription} (Ctrl+Z)`:"没有可撤销的操作"},[t[23]||(t[23]=g("span",{class:"undo-icon"},"↶",-1)),jn(" 撤销 ("+ye(s.gameStore.undoStepsCount)+") ",1)],8,Vm),g("button",{onClick:t[2]||(t[2]=(...a)=>s.resetGame&&s.resetGame(...a)),class:"btn-control"},"重置"),g("button",{onClick:t[3]||(t[3]=(...a)=>s.saveGame&&s.saveGame(...a)),class:"btn-control"},"保存"),g("button",{onClick:t[4]||(t[4]=a=>s.showImportDialog=!0),class:"btn-control"},"导入")])]),g("div",$m,[t[28]||(t[28]=cs('<div class="announcement-panel" data-v-aafb734d><div class="announcement-header" data-v-aafb734d><h3 data-v-aafb734d>小里连连看规则</h3></div><div class="announcement-content" data-v-aafb734d><div class="rule-item" data-v-aafb734d><p data-v-aafb734d>每局结束连线，带出几格红就可连几个</p></div><div class="rule-item" data-v-aafb734d><p data-v-aafb734d>6格以上的红可以存一个，其他格数用不完则白送</p></div><div class="rule-item" data-v-aafb734d><p data-v-aafb734d>大格红可以降级连小格，小格红不能连大格</p></div><div class="rule-item" data-v-aafb734d><p data-v-aafb734d>如6格红可以连3对1格</p></div><div class="rule-item" data-v-aafb734d><p style="color:red;" data-v-aafb734d>注意：连线大格红需要清空其连线路径上的所有小格</p></div></div></div>',1)),g("div",Gm,[t[27]||(t[27]=g("div",{class:"stored-red-header"},[g("h3",null,"暂存大红")],-1)),g("div",Km,[g("div",Wm,[t[24]||(t[24]=g("span",{class:"red-label"},"6格:",-1)),g("button",{onClick:t[5]||(t[5]=a=>s.gameStore.decreaseStoredRed("red6")),class:"btn-adjust",disabled:s.gameStore.storedRed.red6<=0},"-",8,zm),g("span",qm,"("+ye(s.gameStore.storedRed.red6)+")",1),g("button",{onClick:t[6]||(t[6]=a=>s.gameStore.increaseStoredRed("red6")),class:"btn-adjust"},"+")]),g("div",Jm,[t[25]||(t[25]=g("span",{class:"red-label"},"9格:",-1)),g("button",{onClick:t[7]||(t[7]=a=>s.gameStore.decreaseStoredRed("red9")),class:"btn-adjust",disabled:s.gameStore.storedRed.red9<=0},"-",8,Xm),g("span",Ym,"("+ye(s.gameStore.storedRed.red9)+")",1),g("button",{onClick:t[8]||(t[8]=a=>s.gameStore.increaseStoredRed("red9")),class:"btn-adjust"},"+")]),g("div",Qm,[t[26]||(t[26]=g("span",{class:"red-label"},"12格:",-1)),g("button",{onClick:t[9]||(t[9]=a=>s.gameStore.decreaseStoredRed("red12")),class:"btn-adjust",disabled:s.gameStore.storedRed.red12<=0},"-",8,Zm),g("span",eg,"("+ye(s.gameStore.storedRed.red12)+")",1),g("button",{onClick:t[10]||(t[10]=a=>s.gameStore.increaseStoredRed("red12")),class:"btn-adjust"},"+")])])])])])])])]),s.gameStore.isGameCompleted?(z(),J("div",tg,[g("div",ng,[t[30]||(t[30]=g("h2",null,"🎉 恭喜完成!",-1)),g("div",sg,[g("button",{onClick:t[11]||(t[11]=(...a)=>s.resetGame&&s.resetGame(...a)),class:"btn-confirm"},"再来一局"),g("button",{onClick:t[12]||(t[12]=(...a)=>s.backToHome&&s.backToHome(...a)),class:"btn-cancel"},"返回首页")])])])):De("",!0),s.showImportDialog?(z(),J("div",{key:1,class:"dialog-overlay",onClick:t[18]||(t[18]=(...a)=>s.closeImportDialog&&s.closeImportDialog(...a))},[g("div",{class:"dialog",onClick:t[17]||(t[17]=In(()=>{},["stop"]))},[t[31]||(t[31]=g("h3",null,"导入游戏进度",-1)),Hn(g("input",{"onUpdate:modelValue":t[13]||(t[13]=a=>s.importCode=a),type:"text",placeholder:"请输入进度代码",onKeyup:t[14]||(t[14]=Do((...a)=>s.importGame&&s.importGame(...a),["enter"]))},null,544),[[Rs,s.importCode]]),g("div",og,[g("button",{onClick:t[15]||(t[15]=(...a)=>s.closeImportDialog&&s.closeImportDialog(...a)),class:"btn-cancel"},"取消"),g("button",{onClick:t[16]||(t[16]=(...a)=>s.importGame&&s.importGame(...a)),class:"btn-confirm",disabled:!s.importCode.trim()}," 导入 ",8,rg)])])])):De("",!0),s.showSaveSuccess?(z(),J("div",ig,[g("div",lg,[t[33]||(t[33]=g("h3",null,"保存成功!",-1)),g("p",null,[t[32]||(t[32]=jn("进度代码: ",-1)),g("strong",null,ye(s.savedCode),1)]),g("button",{onClick:t[19]||(t[19]=(...a)=>s.copySaveCode&&s.copySaveCode(...a)),class:"btn-copy"},"复制代码"),g("button",{onClick:t[20]||(t[20]=a=>s.showSaveSuccess=!1),class:"btn-close"},"关闭")])])):De("",!0)],64)}const cg=tn(bm,[["render",ag],["__scopeId","data-v-aafb734d"]]);class ug{constructor(){this.audioContext=null,this.isAudioSupported="speechSynthesis"in window,this.voices=[],this.preferredVoice=null,this.initVoices()}initVoices(){if(!this.isAudioSupported)return;const t=()=>{this.voices=speechSynthesis.getVoices(),this.selectPreferredVoice()};t(),speechSynthesis.onvoiceschanged!==void 0&&(speechSynthesis.onvoiceschanged=t)}selectPreferredVoice(){if(this.voices.length===0)return;const t=this.voices.filter(n=>n.lang.includes("zh")||n.name.toLowerCase().includes("chinese")||n.name.includes("中文"));t.length>0?this.preferredVoice=t.find(n=>n.lang.includes("zh-CN")||n.name.includes("普通话"))||t[0]:this.preferredVoice=this.voices[0]}announceResult(t,n={}){return this.isAudioSupported?new Promise((s,o)=>{try{speechSynthesis.cancel();const r=new SpeechSynthesisUtterance;r.text=n.prefix?`${n.prefix}${t}`:`${t}`,r.lang=n.lang||"zh-CN",r.rate=n.rate||.8,r.pitch=n.pitch||1.2,r.volume=n.volume||1,this.preferredVoice&&(r.voice=this.preferredVoice),r.onend=()=>{s()},r.onerror=i=>{console.error("语音播报失败:",i.error),o(i.error)},r.onstart=()=>{},speechSynthesis.speak(r)}catch(r){console.error("创建语音播报失败:",r),o(r)}}):(console.warn("浏览器不支持语音合成"),Promise.resolve())}playSpinSound(){if(!this.audioContext)try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(t){console.warn("无法创建音频上下文:",t);return}try{const t=this.audioContext.createOscillator(),n=this.audioContext.createGain();t.connect(n),n.connect(this.audioContext.destination),t.type="sine",t.frequency.setValueAtTime(200,this.audioContext.currentTime),t.frequency.exponentialRampToValueAtTime(100,this.audioContext.currentTime+.1),n.gain.setValueAtTime(.1,this.audioContext.currentTime),n.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.1),t.start(this.audioContext.currentTime),t.stop(this.audioContext.currentTime+.1)}catch(t){console.warn("播放旋转音效失败:",t)}}playResultSound(){if(!this.audioContext)try{this.audioContext=new(window.AudioContext||window.webkitAudioContext)}catch(t){console.warn("无法创建音频上下文:",t);return}try{const t=this.audioContext.createOscillator(),n=this.audioContext.createGain();t.connect(n),n.connect(this.audioContext.destination),t.type="sine",t.frequency.setValueAtTime(400,this.audioContext.currentTime),t.frequency.exponentialRampToValueAtTime(800,this.audioContext.currentTime+.3),n.gain.setValueAtTime(.2,this.audioContext.currentTime),n.gain.exponentialRampToValueAtTime(.01,this.audioContext.currentTime+.3),t.start(this.audioContext.currentTime),t.stop(this.audioContext.currentTime+.3)}catch(t){console.warn("播放结果音效失败:",t)}}stopAll(){if(this.isAudioSupported&&speechSynthesis.cancel(),this.audioContext)try{this.audioContext.close(),this.audioContext=null}catch(t){console.warn("关闭音频上下文失败:",t)}}checkAudioSupport(){return{speechSynthesis:"speechSynthesis"in window,audioContext:!!(window.AudioContext||window.webkitAudioContext),voices:this.voices.length>0,preferredVoice:this.preferredVoice?this.preferredVoice.name:null}}getAvailableVoices(){return this.voices.map(t=>({name:t.name,lang:t.lang,localService:t.localService,default:t.default}))}}const _o=new ug,nc=ma("wheel",{state:()=>({segments:["苹果","香蕉","橙子","葡萄","草莓","西瓜","桃子","梨","菠萝","芒果","樱桃","柠檬","猕猴桃","蓝莓","石榴","椰子"],isSpinning:!1,currentRotation:0,selectedSegment:null,selectedIndex:-1,spinHistory:[],audioEnabled:!0,spinDuration:3e3,minSpins:5,maxSpins:8,storageKey:"wheelGameData"}),getters:{segmentAngle(){return 360/this.segments.length},segmentConfigs(){const e=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA","#F1948A","#85C1E9","#D7BDE2","#A3E4D7","#F9E79F","#D5A6BD","#AED6F1","#A9DFBF"];return this.segments.map((t,n)=>({id:n,text:t,startAngle:n*this.segmentAngle,endAngle:(n+1)*this.segmentAngle,color:e[n%e.length]}))},getPointerSegmentIndex(){const e=(360-this.currentRotation%360+360)%360;return Math.floor(e/this.segmentAngle)%this.segments.length}},actions:{async spinWheel(){if(this.isSpinning)return;this.isSpinning=!0,this.selectedSegment=null,this.selectedIndex=-1,this.audioEnabled&&_o.playSpinSound();const e=this.minSpins+Math.random()*(this.maxSpins-this.minSpins),t=Math.random()*360,n=this.segmentAngle/2,s=Math.floor(t/this.segmentAngle)*this.segmentAngle+n,o=e*360+s;this.currentRotation+=o,setTimeout(()=>{this.isSpinning=!1,this.selectedIndex=this.getPointerSegmentIndex,this.selectedSegment=this.segments[this.selectedIndex],this.addToHistory(this.selectedSegment),this.audioEnabled&&(_o.playResultSound(),setTimeout(()=>{this.announceResult(this.selectedSegment)},500)),this.saveToLocalStorage()},this.spinDuration)},addToHistory(e){const t={id:Date.now(),result:e,timestamp:new Date().toLocaleString("zh-CN"),date:new Date};this.spinHistory.unshift(t),this.spinHistory.length>100&&(this.spinHistory=this.spinHistory.slice(0,100))},async announceResult(e){try{await _o.announceResult(e,{prefix:"",rate:.8,pitch:1.2})}catch(t){console.error("播报失败:",t)}},toggleAudio(){this.audioEnabled=!this.audioEnabled,this.saveToLocalStorage()},updateSegments(e){if(e.length<2||e.length>40)throw new Error("段落数量必须在2-40之间");this.segments=[...e],this.currentRotation=0,this.selectedSegment=null,this.selectedIndex=-1,this.saveToLocalStorage()},clearHistory(){this.spinHistory=[],this.saveToLocalStorage()},resetGame(){this.currentRotation=0,this.selectedSegment=null,this.selectedIndex=-1,this.spinHistory=[],this.isSpinning=!1,this.saveToLocalStorage()},saveToLocalStorage(){try{const e={segments:this.segments,currentRotation:this.currentRotation,selectedSegment:this.selectedSegment,selectedIndex:this.selectedIndex,spinHistory:this.spinHistory,audioEnabled:this.audioEnabled,lastSaved:new Date().toISOString()};localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存游戏数据失败:",e)}},loadFromLocalStorage(){try{const e=localStorage.getItem(this.storageKey);if(e){const t=JSON.parse(e);return t.segments&&(this.segments=t.segments),t.currentRotation!==void 0&&(this.currentRotation=t.currentRotation),t.selectedSegment&&(this.selectedSegment=t.selectedSegment),t.selectedIndex!==void 0&&(this.selectedIndex=t.selectedIndex),t.spinHistory&&(this.spinHistory=t.spinHistory),t.audioEnabled!==void 0&&(this.audioEnabled=t.audioEnabled),!0}return!1}catch(e){return console.error("加载游戏数据失败:",e),!1}},clearLocalStorage(){try{localStorage.removeItem(this.storageKey)}catch(e){console.error("清除本地存储失败:",e)}}}});const fg={name:"SpinWheel",emits:["spin"],setup(e,{emit:t}){const n=nc(),s=()=>{n.isSpinning||t("spin")},o=c=>{var y;if(!c)return"16px";const f=c.length,h=((y=n.segmentConfigs)==null?void 0:y.length)||8;let m=20;return h>16?m=14:h>12?m=16:h>8&&(m=18),f<=2?`${m}px`:f<=3?`${Math.max(m-1,12)}px`:f<=4?`${Math.max(m-2,12)}px`:f<=5?`${Math.max(m-3,12)}px`:`${Math.max(m-4,10)}px`},r=c=>parseInt(o(c))*1.2,i=c=>{const f=c*Math.PI/180,h=50+50*Math.cos(f-Math.PI/2),m=50+50*Math.sin(f-Math.PI/2);return`polygon(50% 50%, 50% 0%, ${h}% ${m}%)`},l=c=>{const S=360/n.segmentConfigs.length,_=c*S-90,O=_+S,T=_*Math.PI/180,C=O*Math.PI/180,P=200+180*Math.cos(T),U=200+180*Math.sin(T),X=200+180*Math.cos(C),q=200+180*Math.sin(C),K=200+60*Math.cos(C),N=200+60*Math.sin(C),W=200+60*Math.cos(T),te=200+60*Math.sin(T),D=S>180?1:0;return`M ${P} ${U} A 180 180 0 ${D} 1 ${X} ${q} L ${K} ${N} A 60 60 0 ${D} 0 ${W} ${te} Z`},a=c=>{const y=360/n.segmentConfigs.length,_=(c*y+y/2-90)*Math.PI/180;return{x:200+110*Math.cos(_),y:200+110*Math.sin(_)}};return{wheelStore:n,handleCenterClick:s,getTextSize:o,getCharSpacing:r,getSegmentClipPath:i,getSegmentPath:l,getTextPosition:a,getTextTransform:c=>{const f=360/n.segmentConfigs.length,h=c*f+f/2-90,m=a(c);let y=h+90;return y=(y%360+360)%360,`rotate(${y}, ${m.x}, ${m.y})`}}}},dg={class:"spin-wheel-container"},hg={class:"wheel-frame"},pg={class:"wheel-svg",viewBox:"0 0 400 400"},mg=["d","fill"],gg={key:0},yg=["x","y","transform"],bg={class:"center-content"},vg={class:"center-icon"},Sg={class:"center-text"},wg={key:0,class:"result-highlight"};function _g(e,t,n,s,o,r){return z(),J("div",dg,[g("div",hg,[t[1]||(t[1]=g("div",{class:"pointer"},[g("div",{class:"pointer-triangle"})],-1)),g("div",{class:mt(["wheel",{spinning:s.wheelStore.isSpinning,"has-result":s.wheelStore.selectedSegment}]),style:Et({transform:`rotate(${s.wheelStore.currentRotation}deg)`})},[(z(),J("svg",pg,[(z(!0),J(ve,null,gt(s.wheelStore.segmentConfigs,(i,l)=>(z(),J("g",{key:i.id},[g("path",{d:s.getSegmentPath(l),fill:i.color,class:mt([{"segment-selected":s.wheelStore.selectedIndex===l&&!s.wheelStore.isSpinning,"segment-highlighted":s.wheelStore.selectedIndex===l},"wheel-segment-path"]),stroke:"rgba(255,255,255,0.2)","stroke-width":"1"},null,10,mg),i.text?(z(),J("g",gg,[(z(!0),J(ve,null,gt(i.text,(a,u)=>(z(),J("text",{key:u,x:s.getTextPosition(l).x,y:s.getTextPosition(l).y+(u-(i.text.length-1)/2)*s.getCharSpacing(i.text),transform:s.getTextTransform(l),class:"segment-text-svg",style:Et({fontSize:s.getTextSize(i.text)}),"text-anchor":"middle","dominant-baseline":"central",fill:"#ffffff",stroke:"#333333","stroke-width":"0.2","font-weight":"600","font-family":"Arial, Microsoft YaHei, sans-serif"},ye(a),13,yg))),128))])):De("",!0)]))),128))])),g("div",{class:mt(["wheel-center",{clickable:!s.wheelStore.isSpinning}]),onClick:t[0]||(t[0]=(...i)=>s.handleCenterClick&&s.handleCenterClick(...i))},[g("div",bg,[g("div",vg,ye(s.wheelStore.isSpinning?"⏳":"🎯"),1),g("div",Sg,ye(s.wheelStore.isSpinning?"转动中":"SPIN"),1)])],2)],6),t[2]||(t[2]=g("div",{class:"wheel-shadow"},null,-1))]),s.wheelStore.selectedSegment&&!s.wheelStore.isSpinning?(z(),J("div",wg)):De("",!0)])}const xg=tn(fg,[["render",_g],["__scopeId","data-v-78ecf858"]]);const Cg={name:"NewWheelGame",components:{SpinWheel:xg},setup(){const e=nc(),t=ge(!1),n=ge(!1),s=ge(""),o=ge(0),r=ge([{name:"大红转盘",icon:"🔴",segments:["微型反应炉","碳纤维板","刀片服务器","万金泪冠","电台","喇叭机","火箭燃油","步战车","炮弹","纵横","人头像","坦克","非洲之心","暖气片","除颤仪","医疗机器人","高速阵列","ECMO","复苏呼吸机","动力电池组","装甲车电池","强力吸尘器","超算","黑匣子","笔记本","海洋之泪","浮力设备","扫地机器人","云存储","绝密服务器"]},{name:"小红转盘",icon:"🟠",segments:["无人机","小终端","大终端","显卡","呼吸机","香槟","鱼子酱","滑膛枪","天圆地方","瞪羚","化石","金条","怀表","机械表","量子存储","实验数据","咖啡豆","摄影机","锅"]},{name:"航天房卡",icon:"🚀",segments:["总裁会议室","蓝室数据中心","组装车间","蓝室玻璃房","蓝室核心","黒室服务器","3号宿舍里屋","中控室三楼","浮力室医务室"]},{name:"巴克什房卡",icon:"🏠",segments:["博物馆监控室","西城民宅","东城民宅","总裁1间","地下金库1间","旅店用餐间","Relink","博物馆展览套间","老浴场贵宾室","博物馆废弃展厅","医疗会议室","1号审讯室","生物数据机房","海洋监测厅","老浴场餐厅","门诊室","旅店员工休息室"]}]),i=we(()=>n.value?e.spinHistory:e.spinHistory.slice(0,10)),l=C=>{o.value=C;const P=r.value[C];e.updateSegments(P.segments),e.clearHistory()},a=()=>{l(0)},u=()=>{e.spinWheel()},c=()=>{e.toggleAudio()},f=()=>{confirm("确定要清除所有抽奖记录吗？此操作不可恢复。")&&(e.clearHistory(),n.value=!1)},h=()=>{confirm("确定要重置游戏吗？这将清除所有数据。")&&(e.resetGame(),n.value=!1)},m=()=>{window.location.href="/"},y=()=>{t.value=!1,s.value=e.segments.join(`
`)},S=()=>{const C=s.value.split(`
`).map(P=>P.trim()).filter(P=>P.length>0);if(C.length<2){alert("至少需要2个选项！");return}if(C.length>40){alert("最多支持40个选项！");return}try{e.updateSegments(C),y(),alert("设置已应用！")}catch(P){alert("应用设置失败："+P.message)}},_=()=>{if(confirm("确定要恢复默认转盘内容吗？")){const C=["苹果","香蕉","橙子","葡萄","草莓","西瓜","桃子","梨","菠萝","芒果","樱桃","柠檬","猕猴桃","蓝莓","石榴","椰子"];e.updateSegments(C),s.value=C.join(`
`),alert("已恢复默认设置！")}},O=()=>{try{const C={exportTime:new Date().toLocaleString("zh-CN"),totalCount:e.spinHistory.length,history:e.spinHistory},P=new Blob([JSON.stringify(C,null,2)],{type:"application/json"}),U=URL.createObjectURL(P),X=document.createElement("a");X.href=U,X.download=`转盘游戏记录_${new Date().toISOString().slice(0,10)}.json`,X.click(),URL.revokeObjectURL(U)}catch(C){alert("导出失败："+C.message)}},T=()=>new Date().toLocaleString("zh-CN");return js(()=>{e.loadFromLocalStorage()?confirm(`检测到之前的游戏数据，是否要加载？

点击"确定"加载之前的数据
点击"取消"开始新游戏`)||(e.resetGame(),a()):a(),s.value=e.segments.join(`
`)}),{wheelStore:e,showSettings:t,showAllHistory:n,customSegments:s,displayHistory:i,wheelOptions:r,selectedWheelIndex:o,selectWheel:l,spinWheel:u,toggleAudio:c,clearHistory:f,resetGame:h,goHome:m,closeSettings:y,applyCustomSegments:S,resetToDefault:_,exportHistory:O,getCurrentTime:T}}},Eg={class:"new-wheel-game"},Rg={class:"game-header"},Tg={class:"control-buttons"},Ag=["title"],Pg={class:"game-main"},Og={class:"game-section"},Ig={class:"wheel-history-container"},Lg={class:"wheel-selector-panel"},kg={class:"selector-card"},Ng={class:"selector-options"},Dg=["onClick"],Mg={class:"option-icon"},Fg={class:"option-name"},Hg={class:"wheel-section"},Ug={class:"wheel-card"},Bg={class:"right-panel"},jg={class:"result-panel"},Vg={key:0,class:"result-section"},$g={class:"result-content"},Gg={class:"result-text"},Kg={class:"result-time"},Wg={class:"history-panel"},zg={class:"history-header"},qg={class:"history-count"},Jg={class:"history-content"},Xg={key:0,class:"no-history"},Yg={key:1,class:"history-list"},Qg={class:"history-result"},Zg={class:"result-badge"},ey={class:"history-meta"},ty={class:"history-time"},ny={class:"history-index"},sy={key:0,class:"more-history"},oy={class:"export-section"},ry={class:"settings-header"},iy={class:"settings-body"},ly={class:"setting-group"},ay={class:"setting-group"},cy={class:"setting-options"},uy={class:"option-item"},fy={class:"settings-footer"};function dy(e,t,n,s,o,r){const i=Ss("SpinWheel");return z(),J("div",Eg,[g("div",Rg,[g("div",Tg,[g("button",{onClick:t[0]||(t[0]=(...l)=>s.goHome&&s.goHome(...l)),class:"control-btn home",title:"返回首页"},t[16]||(t[16]=[g("span",null,"首页",-1)])),g("button",{onClick:t[1]||(t[1]=(...l)=>s.toggleAudio&&s.toggleAudio(...l)),class:mt(["control-btn",{active:s.wheelStore.audioEnabled}]),title:s.wheelStore.audioEnabled?"关闭音效":"开启音效"},[jn(ye(s.wheelStore.audioEnabled?"🔊":"🔇")+" ",1),t[17]||(t[17]=g("span",null,"音效",-1))],10,Ag),g("button",{onClick:t[2]||(t[2]=l=>s.showSettings=!0),class:"control-btn",title:"转盘设置"},t[18]||(t[18]=[g("span",null,"设置",-1)])),g("button",{onClick:t[3]||(t[3]=(...l)=>s.clearHistory&&s.clearHistory(...l)),class:"control-btn danger",title:"清除历史记录"},t[19]||(t[19]=[g("span",null,"清除",-1)])),g("button",{onClick:t[4]||(t[4]=(...l)=>s.resetGame&&s.resetGame(...l)),class:"control-btn warning",title:"重置游戏"},t[20]||(t[20]=[g("span",null,"重置",-1)]))])]),g("div",Pg,[g("div",Og,[t[25]||(t[25]=g("div",{class:"game-title-section"},[g("h1",null,"🎯 幸运转盘")],-1)),g("div",Ig,[g("div",Lg,[g("div",kg,[t[21]||(t[21]=g("div",{class:"selector-header"},[g("h3",null,"🎯 选择转盘")],-1)),g("div",Ng,[(z(!0),J(ve,null,gt(s.wheelOptions,(l,a)=>(z(),J("div",{key:a,class:mt(["selector-option",{active:s.selectedWheelIndex===a}]),onClick:u=>s.selectWheel(a)},[g("div",Mg,ye(l.icon),1),g("div",Fg,ye(l.name),1)],10,Dg))),128))])])]),g("div",Hg,[g("div",Ug,[Se(i,{onSpin:s.spinWheel},null,8,["onSpin"])])]),g("div",Bg,[g("div",jg,[Se(Kr,{name:"result-fade"},{default:rs(()=>[s.wheelStore.selectedSegment&&!s.wheelStore.isSpinning?(z(),J("div",Vg,[t[22]||(t[22]=g("div",{class:"result-header"},[g("span",{class:"result-icon"},"🎉"),g("h3",null,"恭喜您抽中了")],-1)),g("div",$g,[g("div",Gg,ye(s.wheelStore.selectedSegment),1),g("div",Kg,ye(s.getCurrentTime()),1)])])):De("",!0)]),_:1})]),g("div",Wg,[g("div",zg,[t[23]||(t[23]=g("h3",null,"抽奖记录",-1)),g("span",qg,ye(s.wheelStore.spinHistory.length)+" 次",1)]),g("div",Jg,[s.wheelStore.spinHistory.length===0?(z(),J("div",Xg,t[24]||(t[24]=[g("div",{class:"no-history-text"},"暂无抽奖记录",-1),g("div",{class:"no-history-tip"},'点击"开始转动"进行第一次抽奖吧！',-1)]))):(z(),J("div",Yg,[Se(xf,{name:"history-item",tag:"div"},{default:rs(()=>[(z(!0),J(ve,null,gt(s.displayHistory,l=>(z(),J("div",{key:l.id,class:"history-item"},[g("div",Qg,[g("span",Zg,ye(l.result),1)]),g("div",ey,[g("span",ty,ye(l.timestamp),1),g("span",ny,"#"+ye(s.wheelStore.spinHistory.indexOf(l)+1),1)])]))),128))]),_:1}),s.wheelStore.spinHistory.length>10?(z(),J("div",sy,[g("button",{onClick:t[5]||(t[5]=l=>s.showAllHistory=!s.showAllHistory),class:"more-btn"},ye(s.showAllHistory?"收起":`还有 ${s.wheelStore.spinHistory.length-10} 条记录...`),1)])):De("",!0),g("div",oy,[s.wheelStore.spinHistory.length>0?(z(),J("button",{key:0,onClick:t[6]||(t[6]=(...l)=>s.exportHistory&&s.exportHistory(...l)),class:"export-btn",title:"导出记录"}," 📤 导出记录 ")):De("",!0)])]))])])])])])]),Se(Kr,{name:"modal"},{default:rs(()=>[s.showSettings?(z(),J("div",{key:0,class:"settings-modal",onClick:t[15]||(t[15]=(...l)=>s.closeSettings&&s.closeSettings(...l))},[g("div",{class:"settings-content",onClick:t[14]||(t[14]=In(()=>{},["stop"]))},[g("div",ry,[t[26]||(t[26]=g("h3",null,"转盘设置",-1)),g("button",{onClick:t[7]||(t[7]=(...l)=>s.closeSettings&&s.closeSettings(...l)),class:"close-btn"},"✕")]),g("div",iy,[g("div",ly,[t[27]||(t[27]=g("label",{class:"setting-label"},"自定义转盘内容：",-1)),Hn(g("textarea",{"onUpdate:modelValue":t[8]||(t[8]=l=>s.customSegments=l),class:"setting-textarea",placeholder:`请输入转盘内容，每行一个项目
例如：
苹果
香蕉
橙子`,rows:"8"},null,512),[[Rs,s.customSegments]]),t[28]||(t[28]=g("div",{class:"setting-tip"},[g("span",{class:"tip-icon"},"💡"),jn(" 每行输入一个选项，支持2-40个选项 ")],-1))]),g("div",ay,[t[30]||(t[30]=g("label",{class:"setting-label"},"音效设置：",-1)),g("div",cy,[g("label",uy,[Hn(g("input",{type:"checkbox","onUpdate:modelValue":t[9]||(t[9]=l=>s.wheelStore.audioEnabled=l),onChange:t[10]||(t[10]=l=>s.wheelStore.saveToLocalStorage())},null,544),[[Pf,s.wheelStore.audioEnabled]]),t[29]||(t[29]=g("span",null,"启用音效和语音播报",-1))])])])]),g("div",fy,[g("button",{onClick:t[11]||(t[11]=(...l)=>s.applyCustomSegments&&s.applyCustomSegments(...l)),class:"apply-btn"}," ✓ 应用设置 "),g("button",{onClick:t[12]||(t[12]=(...l)=>s.resetToDefault&&s.resetToDefault(...l)),class:"reset-btn"}," 🔄 恢复默认 "),g("button",{onClick:t[13]||(t[13]=(...l)=>s.closeSettings&&s.closeSettings(...l)),class:"cancel-btn"}," ✕ 取消 ")])])])):De("",!0)]),_:1})])}const hy=tn(Cg,[["render",dy],["__scopeId","data-v-756fb80f"]]),py=[{path:"/",name:"Home",component:Qp},{path:"/game/:sessionId?",name:"Game",component:cg,props:!0},{path:"/wheel",name:"WheelGame",component:hy}],my=zd({history:_d(),routes:py}),wr=Mf(Zd),gy=Bf();wr.use(gy);wr.use(my);wr.mount("#app");
