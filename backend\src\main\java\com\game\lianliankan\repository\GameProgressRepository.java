package com.game.lianliankan.repository;

import com.game.lianliankan.entity.GameProgress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface GameProgressRepository extends JpaRepository<GameProgress, Long> {
    
    Optional<GameProgress> findByProgressCode(String progressCode);
    
    void deleteByProgressCode(String progressCode);
}
